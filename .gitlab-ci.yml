image: docker:latest

# Required variables for ci-engine-dotnet.yml
variables:
  PROJECT_URL: '$CI_SERVER_HOST:5050/marketplace-co/zifysettlement'
  DOCKER_TLS_CERTDIR: '/certs'
  BINARIES_DIRECTORY: 'src/*/bin'
  OBJECTS_DIRECTORY: 'src/*/obj'
  KUBECONFIG: '/root/.kube/config'
  DOTNETRUNV: '$DOTNET9RUNHOST'
  DOTNETSDKV: '$DOTNET9SDKHOST'

include:
  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: stages/dotnet-app.yml
    ref: master
    rules:
      - if: $Internal_Gitlab == "Yes"

  # Include CI engine for Main API
  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: ci-engine-dotnet.yml
    ref: master
    rules:
      - if: $Internal_Gitlab == "Yes"
    inputs:
      job_suffix: 'api'
      project_path: 'src/Api/Api.csproj'
      dll_filename: 'Zify.Settlement.Api.dll'
      deployment_name: 'zify-settlement-api'
      project_type: 'dotnet-app'
      port: 5090