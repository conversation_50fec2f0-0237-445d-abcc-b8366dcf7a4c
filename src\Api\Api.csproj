<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<AssemblyName>Zify.Settlement.$(MSBuildProjectName)</AssemblyName>
		<RootNamespace>$(AssemblyName)</RootNamespace>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
		<PackageReference Include="System.Text.Encodings.Web" Version="9.0.8" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Application\Application.csproj" />
	</ItemGroup>

</Project>
