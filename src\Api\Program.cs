var builder = WebApplication.CreateBuilder(args);

// Configure Serilog early in the pipeline
builder.UseCustomSerilog();

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddCors(corsOption =>
{
    corsOption.AddPolicy(ApplicationConstants.CorsPolicy,
        policy => policy.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader()
            .WithExposedHeaders("X-PayPingRequest-ID")
            .SetPreflightMaxAge(TimeSpan.FromMinutes(10)));
});

builder.Services.AddProblemDetails(options =>
{
    options.CustomizeProblemDetails = context =>
    {
        context.ProblemDetails.Instance = $"{context.HttpContext.Request.Method} {context.HttpContext.Request.Path}";
        context.ProblemDetails.Extensions.TryAdd("requestId", context.HttpContext.TraceIdentifier);
        var activity = context.HttpContext.Features.Get<IHttpActivityFeature>()?.Activity;
        context.ProblemDetails.Extensions.TryAdd("traceId", activity?.TraceId);
    };
});
builder.Services.AddExceptionHandler<GlobalExceptionHandler>();

// Add Application services
builder.Services.AddApplication(builder.Configuration);

// Only add infrastructure if not in testing environment
if (!builder.Environment.IsEnvironment("Testing"))
{
    builder.Services.AddInfrastructure(builder.Configuration, builder.Environment);
}

builder.Services.AddHealthChecks();
builder.Services.AddHttpContextAccessor();

// Configure Kestrel for gRPC (Admin API uses different ports)
builder.WebHost.ConfigureKestrel(options =>
{
    // Configure HTTP/2 endpoint for gRPC
    options.ListenAnyIP(5001, listenOptions =>
    {
        listenOptions.Protocols = HttpProtocols.Http2;
    });

    // Configure HTTPS endpoints
    options.ListenAnyIP(5090, listenOptions =>
    {
        listenOptions.Protocols = HttpProtocols.Http1AndHttp2;
    });
});

var app = builder.Build();

app.UseCustomSwagger();

app.UseCors(ApplicationConstants.CorsPolicy);

app.UseHttpsRedirection();

app.UseCustomExceptionHandler();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Map gRPC services
app.MapGrpcServices();

// Map health checks
app.MapHealthChecks("/health");

app.Run();

public partial class Program
{
}