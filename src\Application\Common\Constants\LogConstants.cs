﻿using Microsoft.Extensions.Logging;

namespace Zify.Settlement.Application.Common.Constants;

public static class LogMessages
{
    public const string SettlementAuthenticatorTotpEnabledMessage =
        "Authenticator Totp enabled for user with Id: {userId}";

    public const string SettlementAuthenticatorTotpDisabledMessage =
        "Authenticator Totp disabled for user with Id: {userId}";

    public const string SettlementAuthenticatorTotpDisabledByAdminMessage =
        "Authenticator Totp disabled for user with Id: {userId} by Administrator";
}

public static class LogEventIds
{
    public static readonly EventId SettlementAuthenticatorTotpEnabled =
        new((int)EventTypes.SettlementAuthenticatorTotpEnabled,
            nameof(EventTypes.SettlementAuthenticatorTotpEnabled));

    public static readonly EventId SettlementAuthenticatorTotpDisabled =
        new((int)EventTypes.SettlementAuthenticatorTotpDisabled,
            nameof(EventTypes.SettlementAuthenticatorTotpDisabled));

    private enum EventTypes
    {
        SettlementAuthenticatorTotpEnabled,
        SettlementAuthenticatorTotpDisabled,
    }
}