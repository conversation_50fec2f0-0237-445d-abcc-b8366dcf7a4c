﻿using ErrorOr;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Common.Interfaces.InternalServices;
public interface IPayeeService
{
    Task<ErrorOr<Payee>> GetOrCreatePayeeAsync(
        PayeeInfo info,
        int userConfigId,
        CancellationToken cancellationToken = default);
}

public record PayeeInfo(string NationalCode, string MobileNumber, Iban Iban, string? FullName);
