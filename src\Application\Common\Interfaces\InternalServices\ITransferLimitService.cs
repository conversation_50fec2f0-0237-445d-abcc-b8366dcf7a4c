﻿using ErrorOr;

namespace Zify.Settlement.Application.Common.Interfaces.InternalServices;

public interface ITransferLimitService
{
    Task<ErrorOr<Success>> CheckDailyTransferLimitAsync(
        int? userId,
        decimal amount,
        decimal dailyLimit,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<Success>> CheckIbanTransferLimitAsync(
        int? userId,
        string iban,
        decimal amount,
        decimal limit,
        CancellationToken cancellationToken = default);
}