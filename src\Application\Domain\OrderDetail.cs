﻿using Ardalis.GuardClauses;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;

public class OrderDetail : AuditableEntity
{
    public Guid Id { get; private init; }
    public string Description { get; private set; } = string.Empty;
    public OrderDetailStatus Status { get; private set; }
    public decimal Amount { get; private set; }
    public decimal WageAmount { get; private set; }
    public int UserId { get; private set; }

    // Payee relationship - can be null for basic orders
    public int? PayeeId { get; private set; }
    public Payee? Payee { get; private set; }

    public Iban Iban { get; private set; }
    public string? Mobile { get; private set; }
    public string? NationalId { get; private set; }
    public string? FullName { get; private set; }

    public CorrelationId? WageTransferTransactionId { get; private set; }
    public bool WageTransactionStatus { get; private set; }


    public Order Order { get; private set; }
    public Guid OrderId { get; private set; }
    public OrderDetailRollbackInfo? OrderDetailRollbackInfo { get; private set; }
    public int? OrderDetailRollbackInfoId { get; private set; }

    private OrderDetail() { }
    public static OrderDetail Create(
        int userId,
        Iban iban,
        decimal amount,
        decimal wageAmount,
        int? payeeId = null,
        string? nationalId = null,
        string? mobile = null,
        string? fullname = null,
        string? description = null)
    {
        Guard.Against.NullOrWhiteSpace(iban, nameof(iban));
        Guard.Against.NegativeOrZero(amount, nameof(amount));
        Guard.Against.Negative(wageAmount, nameof(wageAmount));
        Guard.Against.NegativeOrZero(userId, nameof(userId));

        var id = Guid.CreateVersion7();
        return new OrderDetail
        {
            Id = id,
            Description = description ?? $"آیتم - {id}",
            Status = OrderDetailStatus.Init,
            Iban = Iban.Of(iban),
            Amount = amount,
            WageAmount = wageAmount,
            PayeeId = payeeId,
            NationalId = nationalId,
            Mobile = mobile,
            FullName = fullname,
            UserId = userId,
        };
    }

    public static OrderDetail CreateFromPayee(
        int userId,
        Payee payee,
        Iban selectedIban,
        decimal amount,
        decimal wageAmount,
        string? description = null)
    {
        Guard.Against.Null(payee, nameof(payee));
        Guard.Against.NullOrWhiteSpace(selectedIban, nameof(selectedIban));
        Guard.Against.NegativeOrZero(userId, nameof(userId));

        // Validate that the selected IBAN belongs to the payee
        if (payee.Ibans.All(i => i.Iban != selectedIban))
            throw new ArgumentException("Selected IBAN does not belong to the specified payee", nameof(selectedIban));

        var primaryMobile = payee.MobileNumbers.FirstOrDefault()?.MobileNumber;

        var detail = Create(
            userId: userId,
            iban: selectedIban,
            amount: amount,
            wageAmount: wageAmount,
            nationalId: payee.NationalCode,
            mobile: primaryMobile,
            fullname: payee.FullName,
            description: description);
        detail.Payee = payee;

        return detail;
    }

    public void SetStatus(OrderDetailStatus status)
    {
        Status = status;
    }

    public void SetOrder(Guid orderId)
    {
        Guard.Against.Default(orderId, nameof(orderId));

        if (OrderId != Guid.Empty)
            throw new InvalidOperationException("OrderDetail is already assigned to an order");

        OrderId = orderId;
    }

    public void SetWageTransferResult(CorrelationId transactionId, bool success)
    {
        Guard.Against.NullOrEmpty(transactionId, nameof(transactionId));

        WageTransferTransactionId = transactionId;
        WageTransactionStatus = success;
    }


    public void SetRollbackInfo(Guid rollbackDestinationWalletId)
    {
        Guard.Against.Default(rollbackDestinationWalletId, nameof(rollbackDestinationWalletId));

        if (OrderDetailRollbackInfo != null)
            throw new InvalidOperationException("Rollback info is already set");

        OrderDetailRollbackInfo = OrderDetailRollbackInfo.Create(rollbackDestinationWalletId, Id);
    }

    public void ClearRollbackInfo()
    {
        OrderDetailRollbackInfo = null;
        OrderDetailRollbackInfoId = null;
    }

}

public enum OrderDetailStatus
{
    Init,
    Failed,
    InProgress,
    Success,
}