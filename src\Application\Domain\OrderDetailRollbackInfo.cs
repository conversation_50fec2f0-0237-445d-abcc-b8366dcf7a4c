﻿using Ardalis.GuardClauses;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;

public class OrderDetailRollbackInfo : AuditableEntity
{
    public int Id { get; private set; }
    public CorrelationId RollbackTransferTransactionId { get; private set; }
    public CorrelationId RollbackWageTransactionId { get; private set; }
    public WalletId RollbackDestinationWalletId { get; private set; }
    public DateTimeOffset RollbackRequestedAt { get; private set; }
    public DateTimeOffset? RollbackProcessedAt { get; private set; }
    public string? RollbackTransactionId { get; private set; }
    public bool IsRollbackCompleted { get; private set; }
    public string? RollbackFailureReason { get; private set; }


    public OrderDetail OrderDetail { get; set; }
    public Guid OrderDetailId { get; set; }

    private OrderDetailRollbackInfo() { }

    public static OrderDetailRollbackInfo Create(
        Guid rollbackDestinationWalletId,
        Guid orderDetailId)
    {
        Guard.Against.Default(rollbackDestinationWalletId, nameof(rollbackDestinationWalletId));
        Guard.Against.Default(orderDetailId, nameof(orderDetailId));

        return new OrderDetailRollbackInfo
        {
            RollbackTransferTransactionId = CorrelationId.New(),
            RollbackWageTransactionId = CorrelationId.New(),
            RollbackDestinationWalletId = WalletId.Of(rollbackDestinationWalletId),
            OrderDetailId = orderDetailId,
            RollbackRequestedAt = DateTime.UtcNow,
        };
    }

    public void SetRollbackResult(string transactionId, bool success, string? failureReason = null)
    {
        Guard.Against.NullOrWhiteSpace(transactionId, nameof(transactionId));

        if (IsRollbackCompleted)
            throw new InvalidOperationException("Rollback is already completed");

        RollbackTransactionId = transactionId;
        IsRollbackCompleted = success;
        RollbackProcessedAt = DateTime.UtcNow;

        if (!success)
            RollbackFailureReason = failureReason;
    }
}