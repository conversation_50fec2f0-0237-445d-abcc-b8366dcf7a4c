﻿namespace Zify.Settlement.Application.Domain.OwnedEntities;

public class FavoriteIban
{
    public int PayeeUserConfigId { get; private set; }
    public string Iban { get; private set; } = string.Empty;
    public string? Alias { get; private set; }

    private FavoriteIban() { } // EF Constructor
    public FavoriteIban(int payeeUserConfigId, string iban, string? alias = null)
    {
        PayeeUserConfigId = payeeUserConfigId;
        Iban = iban;
        Alias = alias;
    }
}