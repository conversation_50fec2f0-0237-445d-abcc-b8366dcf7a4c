﻿using Ardalis.GuardClauses;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.OwnedEntities;

namespace Zify.Settlement.Application.Domain;
public class Payee : AuditableEntity
{
    public int Id { get; private set; }
    public string? FullName { get; private set; }
    public string NationalCode { get; private set; } = string.Empty;

    private readonly List<PayeeMobileNumber> _mobileNumbers = [];
    public IReadOnlyCollection<PayeeMobileNumber> MobileNumbers => _mobileNumbers.AsReadOnly();

    private readonly List<PayeeIban> _ibans = [];
    public IReadOnlyCollection<PayeeIban> Ibans => _ibans.AsReadOnly();

    private readonly List<PayeeUserConfig> _payeeUserConfigs = [];
    public IReadOnlyCollection<PayeeUserConfig> PayeeUserConfigs => _payeeUserConfigs.AsReadOnly();

    private Payee() { }
    public static Payee Create(string nationalCode, string? fullName = null)
    {
        Guard.Against.NullOrWhiteSpace(nationalCode, nameof(nationalCode));
        return new Payee
        {
            FullName = fullName,
            NationalCode = nationalCode
        };
    }

    public void AddMobileNumber(string mobileNumber)
    {
        Guard.Against.NullOrWhiteSpace(mobileNumber, nameof(mobileNumber));

        if (_mobileNumbers.Any(x => x.MobileNumber == mobileNumber))
            return; // Already exists

        _mobileNumbers.Add(new PayeeMobileNumber(Id, mobileNumber));
    }

    public void RemoveMobileNumber(string mobileNumber)
    {
        Guard.Against.NullOrWhiteSpace(mobileNumber, nameof(mobileNumber));
        var item = _mobileNumbers.FirstOrDefault(x => x.MobileNumber == mobileNumber);
        if (item != null)
            _mobileNumbers.Remove(item);
    }

    public void AddIban(string iban)
    {
        Guard.Against.NullOrWhiteSpace(iban, nameof(iban));

        if (_ibans.Any(x => x.Iban == iban))
            return; // Already exists

        _ibans.Add(new PayeeIban(Id, iban));
    }

    public void RemoveIban(string iban)
    {
        Guard.Against.NullOrWhiteSpace(iban, nameof(iban));
        var item = _ibans.FirstOrDefault(x => x.Iban == iban);
        if (item != null)
            _ibans.Remove(item);
    }

    public void UpdateDetails(string fullName)
    {
        Guard.Against.NullOrWhiteSpace(fullName, nameof(fullName));
        FullName = fullName;
    }
}
