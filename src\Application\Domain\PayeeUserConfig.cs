﻿using Ardalis.GuardClauses;
using Zify.Settlement.Application.Domain.OwnedEntities;

namespace Zify.Settlement.Application.Domain;

public class PayeeUserConfig
{
    public int Id { get; private set; }
    public int PayeeId { get; private set; }
    public int UserConfigId { get; private set; }
    public bool IsBeneficiary { get; private set; }

    private readonly List<FavoriteIban> _favoriteIbans = [];
    public IReadOnlyCollection<FavoriteIban> FavoriteIbans => _favoriteIbans.AsReadOnly();

    // Navigation properties
    public Payee Payee { get; private set; } = null!;
    public UserConfig UserConfig { get; private set; } = null!;

    private PayeeUserConfig() { }

    public static PayeeUserConfig Create(int payeeId, int userConfigId, bool isBeneficiary = false)
    {
        return new PayeeUserConfig
        {
            PayeeId = payeeId,
            UserConfigId = userConfigId,
            IsBeneficiary = isBeneficiary
        };
    }

    public static PayeeUserConfig Create(Payee payee, int userConfigId, bool isBeneficiary = false)
    {
        return new PayeeUserConfig
        {
            Payee = payee,
            UserConfigId = userConfigId,
            IsBeneficiary = isBeneficiary
        };
    }

    public void SetBeneficiaryStatus(bool isBeneficiary)
    {
        IsBeneficiary = isBeneficiary;
    }

    public void AddFavoriteIban(string iban, string? alias = null)
    {
        Guard.Against.NullOrWhiteSpace(iban, nameof(iban));

        if (Payee?.Ibans.All(i => i.Iban != iban) ?? false)
            throw new ArgumentException("Selected IBAN does not belong to the specified payee", nameof(iban));

        if (_favoriteIbans.Any(x => x.Iban == iban))
            return; // Already exists

        _favoriteIbans.Add(new FavoriteIban(Id, iban, alias));
    }

    public void RemoveFavoriteIban(string iban)
    {
        var item = _favoriteIbans.FirstOrDefault(x => x.Iban == iban);
        if (item != null)
            _favoriteIbans.Remove(item);
    }
}