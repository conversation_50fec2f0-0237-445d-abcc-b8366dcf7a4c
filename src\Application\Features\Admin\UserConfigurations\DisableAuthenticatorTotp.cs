﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Admin.UserConfigurations;
public sealed class DisableAuthenticatorTotpController : ApiControllerBase
{
    [HttpPatch("admin/{userId:int}/disable-authenticator-totp")]
    [Authorize("serviceAdministration")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> DisableAuthenticatorTotpAsync(int userId)
    {
        var command = new AdminDisableAuthenticatorTotpCommand(userId);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(id => Ok(id), Problem);
    }
}

public sealed record AdminDisableAuthenticatorTotpCommand(int UserId)
    : IRequest<AdminDisableAuthenticatorTotpCommand, Task<ErrorOr<Updated>>>;

public sealed class AdminDisableAuthenticatorTotpCommandHandler(
    IApplicationDbContext dbContext,
    ILogger<AdminDisableAuthenticatorTotpCommandHandler> logger)
    : IRequestHandler<AdminDisableAuthenticatorTotpCommand, Task<ErrorOr<Updated>>>
{
    public async Task<ErrorOr<Updated>> Handle(
        AdminDisableAuthenticatorTotpCommand request,
        CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs.AsTracking()
            .Where(uc => uc.UserId == request.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is not { AuthenticatorTotpEnabled: true } ||
            string.IsNullOrWhiteSpace(userConfig.AuthenticatorTotpSecretKey))
        {
            return Result.Updated;
        }

        userConfig.AuthenticatorTotpEnabled = false;
        userConfig.AuthenticatorTotpSecretKey = string.Empty;
        await dbContext.SaveChangesAsync(cancellationToken);

        logger.LogInformation(
            LogEventIds.SettlementAuthenticatorTotpDisabled,
            LogMessages.SettlementAuthenticatorTotpDisabledByAdminMessage, request.UserId);


        return Result.Updated;
    }
}
