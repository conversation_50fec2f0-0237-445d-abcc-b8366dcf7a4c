﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Admin.UserConfigurations;

public sealed record UserConfigurationAdminResponse(
    int UserId,
    string AcceptorCode,
    string Iban,
    SettlementPlanType PlanType,
    DateTime? LastIbanChangeTime,
    byte IbanUpdateMaxChangesPerWindow,
    byte IbanUpdateTimeWindowHours,
    Guid? SettlementWalletId,
    Guid PaymentWalletId,
    bool IsCritical,
    bool IsFree,
    bool IsDepositActivate,
    bool IsBanned,
    WageType WageType,
    decimal WageValue,
    int Max,
    int Min,
    int MaxSettlementAmount,
    decimal DailyTransferLimit,
    bool AuthenticatorTotpEnabled)
{
    public static UserConfigurationAdminResponse FromDomain(UserConfig userConfig)
    {
        return new UserConfigurationAdminResponse(
            UserId: userConfig.UserId,
            AcceptorCode: userConfig.AcceptorCode,
            Iban: userConfig.Iban,
            PlanType: userConfig.PlanType,
            LastIbanChangeTime: userConfig.IbanChangeHistory.OrderDescending().FirstOrDefault(),
            IbanUpdateMaxChangesPerWindow: userConfig.IbanUpdateMaxChangesPerWindow,
            IbanUpdateTimeWindowHours: userConfig.IbanUpdateTimeWindowHours,
            SettlementWalletId: userConfig.WalletInformation.SettlementWalletId,
            PaymentWalletId: userConfig.WalletInformation.PaymentWalletId,
            IsCritical: userConfig.IsCritical,
            IsFree: userConfig.IsFree,
            IsDepositActivate: userConfig.IsDepositActivate,
            IsBanned: userConfig.IsBanned,
            WageType: userConfig.WageType,
            WageValue: userConfig.WageValue,
            Max: userConfig.Max,
            Min: userConfig.Min,
            MaxSettlementAmount: userConfig.MaxSettlementAmount,
            DailyTransferLimit: userConfig.DailyTransferLimit,
            AuthenticatorTotpEnabled: userConfig.AuthenticatorTotpEnabled);
    }
}

public sealed class GetUserConfigurationController : ApiControllerBase
{
    [HttpGet("admin/{userId:int}/user-configs")]
    [Authorize("serviceAdministration")]
    [ProducesResponseType<UserConfigurationAdminResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetUserConfigurationAsync(int userId)
    {
        if (userId <= 0)
            return Problem("User ID must be provided.", statusCode: StatusCodes.Status400BadRequest);

        var result =
            await Mediator.Send(new GetUserConfigurationQuery(userId), HttpContext.RequestAborted);

        return result.Match(Ok, Problem);
    }
}

public sealed record GetUserConfigurationQuery(int UserId)
    : IRequest<GetUserConfigurationQuery, Task<ErrorOr<UserConfigurationAdminResponse>>>;

public sealed class GetUserConfigurationQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetUserConfigurationQuery, Task<ErrorOr<UserConfigurationAdminResponse>>>
{
    public async Task<ErrorOr<UserConfigurationAdminResponse>> Handle(
        GetUserConfigurationQuery request,
        CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Include(x => x.WalletInformation)
            .Where(x => x.UserId == request.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null)
            return Error.NotFound(description: $"User configuration for user ID {request.UserId} not found.");

        return UserConfigurationAdminResponse.FromDomain(userConfig);
    }
}
