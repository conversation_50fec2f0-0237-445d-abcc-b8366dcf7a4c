﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Pagination.Abstractions;
using Zify.Settlement.Application.Common.Pagination.Extensions;
using Zify.Settlement.Application.Common.Pagination.Models;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.CashOut.Queries;

public sealed record OrderDetailsReportResponse(
    string Id,
    decimal Amount,
    decimal Wage,
    string OrderId,
    string Iban,
    string? FullName,
    string? Mobile,
    string? NationalId,
    OrderDetailStatus Status,
    DateTimeOffset Created,
    string BankTransferReference,
    DateTimeOffset? SubmittedTime,
    string Description)
{
    public bool IsFavoriteIban { get; set; }

    public static Expression<Func<OrderDetail, OrderDetailsReportResponse>> OrderDetailSelector()
    {
        return detail => new OrderDetailsReportResponse(
            detail.Id.ToString(),
            detail.Amount,
            detail.WageAmount,
            detail.OrderId.ToString(),
            detail.Iban,
            detail.FullName,
            detail.Mobile,
            detail.NationalId,
            detail.Status,
            detail.Created,
            detail.Id.ToString(),
            detail.Order.SubmittedTime,
            detail.Description);
    }

    public static Expression<Func<OldSettlement.Domain.Settlement, OrderDetailsReportResponse>> OldSettlementSelector()
    {
        return settle => new OrderDetailsReportResponse(
            settle.Code,
            settle.Amount,
            settle.Wage,
            settle.RequestCode,
            settle.Iban,
            settle.FullName,
            settle.Mobile,
            settle.NationalId,
            (OrderDetailStatus)settle.Status,
            settle.CreatedOn,
            settle.BankTransferReference,
            settle.SubmittedTime,
            settle.Description
        );
    }
}

public sealed record OrderDetailsFilterRequest(
    string? Id,
    DateTimeOffset? FromDate,
    DateTimeOffset? ToDate,
    string? Iban,
    string? OrderId,
    OrderDetailStatus? Status) : BaseFilterRequest;

public sealed class OrderDetailsReportController : ApiControllerBase
{
    [HttpGet("report/details")]
    [Authorize("read")]
    [ProducesResponseType<PagedResult<OrderDetailsReportResponse>>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> OrderDetailsReport([FromQuery] OrderDetailsFilterRequest filters)
    {
        var query = new OrderDetailsReportQuery(filters);
        var result = await Mediator.Send(query, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record OrderDetailsReportQuery(OrderDetailsFilterRequest Filters)
    : IRequest<OrderDetailsReportQuery, Task<ErrorOr<PagedResult<OrderDetailsReportResponse>>>>;

public sealed class OrderDetailsReportQueryHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<OrderDetailsReportQuery, Task<ErrorOr<PagedResult<OrderDetailsReportResponse>>>>
{
    public async Task<ErrorOr<PagedResult<OrderDetailsReportResponse>>> Handle(
        OrderDetailsReportQuery request,
        CancellationToken cancellationToken)
    {
        // TODO: Add Old Settlement Report

        var query = dbContext.OrderDetails
                .AsNoTracking()
                .Include(x => x.Order)
                .Where(x => x.UserId == currentUserService.UserId);

        query = ApplyFilters(query, request);

        query = ApplySorting(query, request);

        var details = await query
            .ToPagedResultAsync(
                request.Filters.Pagination,
                OrderDetailsReportResponse.OrderDetailSelector(),
                cancellationToken);

        var favoriteIbans = await dbContext.PayeeUserConfigs
            .Where(x => x.UserConfig.UserId == currentUserService.UserId)
            .SelectMany(x => x.FavoriteIbans)
            .Select(x => x.Iban)
            .ToListAsync(cancellationToken);

        var favoriteIbanSet = new HashSet<string>(favoriteIbans);

        foreach (var detail in details.Items)
        {
            if (favoriteIbanSet.Contains(detail.Iban))
            {
                detail.IsFavoriteIban = true;
            }
        }

        return details;
    }

    private static IQueryable<OrderDetail> ApplyFilters(IQueryable<OrderDetail> query, OrderDetailsReportQuery request)
    {
        var filters = request.Filters;

        if (filters.FromDate != null)
        {
            query = query.Where(x => x.Created >= filters.FromDate);
        }

        if (filters.ToDate != null)
        {
            query = query.Where(x => x.Created <= filters.ToDate);
        }

        if (!string.IsNullOrWhiteSpace(filters.Id) && Guid.TryParse(filters.Id, out var id))
        {
            query = query.Where(x => x.Id == id);
        }

        if (filters.Iban != null)
        {
            query = query.Where(x => x.Iban.Value.Contains(filters.Iban));
        }

        if (!string.IsNullOrWhiteSpace(filters.OrderId) && Guid.TryParse(filters.OrderId, out var orderId))
        {
            query = query.Where(x => x.OrderId == orderId);
        }

        if (filters.Status != null)
        {
            query = query.Where(x => x.Status == filters.Status);
        }

        return query;
    }

    private static IQueryable<OrderDetail> ApplySorting(IQueryable<OrderDetail> query, OrderDetailsReportQuery request)
    {
        if (!string.IsNullOrWhiteSpace(request.Filters.SortBy))
        {
            var sortedQuery = query.ApplySort(request.Filters.SortBy, request.Filters.SortDirection);

            if (!ReferenceEquals(sortedQuery, query))
            {
                return sortedQuery;
            }
        }

        return request.Filters.SortBy?.ToLowerInvariant() switch
        {
            nameof(OrderDetail.Amount) => request.Filters.SortDirection == SortDirection.Ascending
                ? query.OrderBy(p => p.Amount).ThenBy(p => p.Id)
                : query.OrderByDescending(p => p.Amount).ThenBy(p => p.Id),

            nameof(OrderDetail.Created) or "created" => request.Filters.SortDirection == SortDirection.Ascending
                ? query.OrderBy(p => p.Created).ThenBy(p => p.Id)
                : query.OrderByDescending(p => p.Created).ThenBy(p => p.Id),

            _ => query.OrderByDescending(p => p.Created).ThenBy(p => p.Id) // Default sorting for consistent pagination
        };
    }
}

public sealed class OrderDetailsReportValidation : AbstractValidator<OrderDetailsReportQuery>
{
    public OrderDetailsReportValidation()
    {
        RuleFor(x => x.Filters)
            .SetValidator(new OrderDetailsFilterRequestValidator());
    }
}
public sealed class OrderDetailsFilterRequestValidator : AbstractValidator<OrderDetailsFilterRequest>
{
    public OrderDetailsFilterRequestValidator()
    {
        RuleFor(x => x.Id)
            .Must(BeValidGuid)
            .WithMessage("Invalid ID format. Must be a valid GUID.")
            .When(x => !string.IsNullOrWhiteSpace(x.Id));

        RuleFor(x => x.OrderId)
            .Must(BeValidGuid)
            .WithMessage("Invalid OrderId format. Must be a valid GUID.")
            .When(x => !string.IsNullOrWhiteSpace(x.OrderId));

        RuleFor(x => x.FromDate)
            .LessThanOrEqualTo(x => x.ToDate)
            .WithMessage("FromDate must be less than or equal to ToDate.")
            .When(x => x.FromDate.HasValue && x.ToDate.HasValue);

        RuleFor(x => x.ToDate)
            .LessThanOrEqualTo(DateTimeOffset.UtcNow.AddDays(1))
            .WithMessage("ToDate cannot be in the future.");

        RuleFor(x => x.Iban)
            .Must(BeValidIban)
            .WithMessage("Invalid IBAN format.")
            .When(x => !string.IsNullOrWhiteSpace(x.Iban));

        RuleFor(x => x.Status)
            .IsInEnum()
            .WithMessage("Invalid order detail status.")
            .When(x => x.Status.HasValue);

        RuleFor(x => x.Pagination.Page)
            .GreaterThan(0)
            .WithMessage("Page number must be greater than 0.");

        RuleFor(x => x.Pagination.PageSize)
            .InclusiveBetween(1, 100)
            .WithMessage("Page size must be between 1 and 100.");
    }
    private static bool BeValidGuid(string id) => Guid.TryParse(id, out _);

    private static bool BeValidIban(string? iban)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(iban))
                return true;
            Iban.Of(iban); return true;
        }
        catch (Exception) { return false; }
    }
}
