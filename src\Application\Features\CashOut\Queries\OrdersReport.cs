﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Pagination.Abstractions;
using Zify.Settlement.Application.Common.Pagination.Extensions;
using Zify.Settlement.Application.Common.Pagination.Models;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.CashOut.Queries;

public sealed record OrdersReportResponse(
    DateTimeOffset CreatedOn,
    string Code,
    decimal TotalWage,
    decimal TotalAmount,
    OrderStatus Status,
    DateTimeOffset? SubmittedTime)
{
    public int FailedCount { get; init; }
    public int SucceededCount { get; init; }
    public int InProgressCount { get; init; }

    public static Expression<Func<Order, OrdersReportResponse>> OrderSelector() =>
        order =>
            new OrdersReportResponse(
                order.Created,
                order.OrderNumber,
                order.TotalWageAmount,
                order.TotalAmount,
                order.Status,
                order.SubmittedTime)
            {
                FailedCount = order.OrderDetails.Sum(x => x.Status == OrderDetailStatus.Failed ? 1 : 0),
                InProgressCount = order.OrderDetails.Sum(x => x.Status == OrderDetailStatus.InProgress ? 1 : 0),
                SucceededCount = order.OrderDetails.Sum(x => x.Status == OrderDetailStatus.Success ? 1 : 0)
            };
}

public sealed record OrdersFilterRequest(
    string? Id,
    DateTimeOffset? FromDate,
    DateTimeOffset? ToDate) : BaseFilterRequest;

public sealed class OrdersReportController : ApiControllerBase
{
    /// <summary>
    /// Retrieves paginated orders report with filtering and sorting capabilities
    /// </summary>
    /// <param name="filters">Filter criteria for orders</param>
    /// <returns>Paginated list of order reports</returns>
    [HttpGet("report/orders")]
    [Authorize("read")]
    [ProducesResponseType<PagedResult<OrdersReportResponse>>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> OrdersReport([FromQuery] OrdersFilterRequest filters)
    {
        var query = new OrdersReportQuery(filters);
        var result = await Mediator.Send(query, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record OrdersReportQuery(OrdersFilterRequest Filters)
    : IRequest<OrdersReportQuery, Task<ErrorOr<PagedResult<OrdersReportResponse>>>>;

public sealed class OrdersReportQueryHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<OrdersReportQuery, Task<ErrorOr<PagedResult<OrdersReportResponse>>>>
{
    public async Task<ErrorOr<PagedResult<OrdersReportResponse>>> Handle(
        OrdersReportQuery request,
        CancellationToken cancellationToken)
    {
        // TODO: Add Old Request Report

        var query = dbContext.Orders
            .AsNoTrackingWithIdentityResolution()
            .Where(x => x.UserId == currentUserService.UserId);

        query = ApplyFilters(query, request);

        query = ApplySorting(query, request);

        var orders = await query
            .ToPagedResultAsync(
                request.Filters.Pagination,
                OrdersReportResponse.OrderSelector(),
                cancellationToken);

        return orders;
    }

    private static IQueryable<Order> ApplyFilters(IQueryable<Order> query, OrdersReportQuery request)
    {
        var filters = request.Filters;

        if (filters.FromDate.HasValue)
        {
            query = query.Where(x => x.Created >= filters.FromDate.Value);
        }

        if (filters.ToDate.HasValue)
        {
            query = query.Where(x => x.Created <= filters.ToDate.Value);
        }

        if (!string.IsNullOrWhiteSpace(filters.Id) && Guid.TryParse(filters.Id, out var guidId))
        {
            query = query.Where(x => x.Id == guidId);
        }

        return query;
    }

    private static IQueryable<Order> ApplySorting(IQueryable<Order> query, OrdersReportQuery request)
    {
        if (!string.IsNullOrWhiteSpace(request.Filters.SortBy))
        {
            var sortedQuery = query.ApplySort(request.Filters.SortBy, request.Filters.SortDirection);

            if (!ReferenceEquals(sortedQuery, query))
            {
                return sortedQuery;
            }
        }

        return request.Filters.SortBy?.ToLowerInvariant() switch
        {
            nameof(Order.TotalAmount) => request.Filters.SortDirection == SortDirection.Ascending
                ? query.OrderBy(p => p.TotalAmount).ThenBy(p => p.Id)
                : query.OrderByDescending(p => p.TotalAmount).ThenBy(p => p.Id),

            nameof(Order.Created) or "created" => request.Filters.SortDirection == SortDirection.Ascending
                ? query.OrderBy(p => p.Created).ThenBy(p => p.Id)
                : query.OrderByDescending(p => p.Created).ThenBy(p => p.Id),

            nameof(Order.SubmittedTime) => request.Filters.SortDirection == SortDirection.Ascending
                ? query.OrderBy(p => p.SubmittedTime).ThenBy(p => p.Id)
                : query.OrderByDescending(p => p.SubmittedTime).ThenBy(p => p.Id),

            _ => query.OrderByDescending(p => p.Created).ThenBy(p => p.Id) // Default sorting for consistent pagination
        };
    }
}

public sealed class OrdersReportQueryValidator : AbstractValidator<OrdersReportQuery>
{
    public OrdersReportQueryValidator()
    {
        RuleFor(x => x.Filters)
            .SetValidator(new OrdersFilterRequestValidator());
    }
}

public sealed class OrdersFilterRequestValidator : AbstractValidator<OrdersFilterRequest>
{
    public OrdersFilterRequestValidator()
    {
        RuleFor(x => x.Id)
            .Must(BeValidGuid)
            .WithMessage("Invalid ID format. Must be a valid GUID.")
            .When(x => !string.IsNullOrWhiteSpace(x.Id));

        RuleFor(x => x.FromDate)
            .LessThanOrEqualTo(x => x.ToDate)
            .WithMessage("FromDate must be less than or equal to ToDate.")
            .When(x => x.FromDate.HasValue && x.ToDate.HasValue);

        RuleFor(x => x.ToDate)
            .LessThanOrEqualTo(DateTimeOffset.UtcNow.AddDays(1))
            .WithMessage("ToDate cannot be in the future.");

        RuleFor(x => x.Pagination.Page)
            .GreaterThan(0)
            .WithMessage("Page number must be greater than 0.");

        RuleFor(x => x.Pagination.PageSize)
            .InclusiveBetween(1, 100)
            .WithMessage("Page size must be between 1 and 100.");
    }

    private static bool BeValidGuid(string? id) => Guid.TryParse(id, out _);
}
