﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.CashOut.Queries;

public sealed record OverviewResponse(
    decimal TotalWithdraw,
    bool IsDepositActivate,
    string ExcelUrl,
    bool AuthenticatorTotpEnabled,
    bool IsCritical);

public sealed class OverviewController : ApiControllerBase
{
    /// <summary>
    /// Retrieves an overview of the user's cash-out details.
    /// </summary>
    /// <remarks>
    /// This endpoint provides information such as the total withdrawal amount, 
    /// deposit activation status, Excel URL for reports, TOTP authenticator status, 
    /// and critical status for the current user.
    /// </remarks>
    /// <returns>
    /// A <see cref="Task{IActionResult}"/> containing an <see cref="OverviewResponse"/> 
    /// on success or a <see cref="ProblemDetails"/> on failure.
    /// </returns>
    /// <response code="200">Returns the overview details successfully.</response>
    /// <response code="400">Returns a problem details object if the request is invalid.</response>
    [HttpGet("overview")]
    [Authorize("read")]
    [ProducesResponseType<OverviewResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Overview()
    {
        var result = await Mediator.Send(new OverviewQuery(), HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record OverviewQuery
    : IRequest<OverviewQuery, Task<ErrorOr<OverviewResponse>>>;

public sealed class OverviewQueryHandler(
    ICurrentUserService currentUserService,
    IApplicationDbContext dbContext)
    : IRequestHandler<OverviewQuery, Task<ErrorOr<OverviewResponse>>>
{
    public async Task<ErrorOr<OverviewResponse>> Handle(OverviewQuery request, CancellationToken cancellationToken)
    {
        if (currentUserService.UserId == null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        var userConfig = await dbContext.UserConfigs
            .Where(x => x.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null)
            return Error.NotFound(description: "اطلاعات کاربری یافت نشد.");

        var excelUrl = userConfig.IsCritical
            ? Environment.GetEnvironmentVariable("PayPing_Settlement_Critical_Excel")
            : Environment.GetEnvironmentVariable("PayPing_Settlement_Normal_Excel");

        var totpEnabled =
            userConfig.AuthenticatorTotpEnabled && !string.IsNullOrWhiteSpace(userConfig.AuthenticatorTotpSecretKey);

        var totalWithdraw = await dbContext.OrderDetails
            .Where(x => x.UserId == currentUserService.UserId)
            .Where(x => x.Status == OrderDetailStatus.InProgress || x.Status == OrderDetailStatus.Success)
            .SumAsync(x => x.Amount, cancellationToken);

        return new OverviewResponse(
            TotalWithdraw: totalWithdraw,
            IsDepositActivate: userConfig.IsDepositActivate,
            ExcelUrl: excelUrl ?? string.Empty,
            AuthenticatorTotpEnabled: totpEnabled,
            IsCritical: userConfig.IsCritical);
    }
}