﻿using DispatchR.Requests.Send;
using DNTPersianUtils.Core;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.InternalServices;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.OrderDetails.Commands;

public sealed record AddOrderDetailResponse(OrderDetailResponse OrderDetail);

public record OrderDetailResponse(
    Guid Id,
    decimal Amount,
    decimal Wage,
    string Iban,
    string FullName,
    string Description)
{
    public static OrderDetailResponse FromDomain(OrderDetail orderDetail) =>
        new(orderDetail.Id,
            orderDetail.Amount,
            orderDetail.WageAmount,
            orderDetail.Iban,
            orderDetail.FullName ?? "استعلام نامشخص",
            orderDetail.Description);
};

public sealed class AddOrderDetailController : ApiControllerBase
{
    /// <summary>
    /// Adds a new order detail to the specified order.
    /// </summary>
    /// <param name="orderId">
    /// The unique identifier of the order to which the detail will be added.
    /// </param>
    /// <param name="request">
    /// The command containing the details of the order to be added, including amount, IBAN, description, mobile, and national ID.
    /// </param>
    /// <returns>
    /// An <see cref="IActionResult"/> representing the result of the operation. 
    /// Returns <see cref="StatusCodes.Status200OK"/> if the operation is successful, 
    /// or <see cref="StatusCodes.Status400BadRequest"/> if the request is invalid.
    /// </returns>
    /// <remarks>
    /// This method requires the "write" authorization policy.
    /// </remarks>
    [HttpPost("{orderId:guid}/add-order-detail")]
    [Authorize("write")]
    [ProducesResponseType<AddOrderDetailResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddOrderDetail(
        [FromRoute] Guid orderId,
        [FromBody] AddOrderDetailCommand request)
    {
        request.OrderId = orderId;
        var result = await Mediator.Send(request, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record AddOrderDetailCommand(
    decimal Amount,
    string Iban,
    string Mobile,
    string NationalId,
    string? Description)
    : IRequest<AddOrderDetailCommand, Task<ErrorOr<AddOrderDetailResponse>>>
{
    [JsonIgnore]
    public Guid OrderId { get; set; }
}

public sealed class AddOrderDetailCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IWageCalculatorService wageCalculatorService,
    IIbanInquiryService ibanInquiryService,
    IPayeeService payeeService,
    ITransferLimitService transferLimitService)
    : IRequestHandler<AddOrderDetailCommand, Task<ErrorOr<AddOrderDetailResponse>>>
{
    public async Task<ErrorOr<AddOrderDetailResponse>> Handle(
        AddOrderDetailCommand request,
        CancellationToken cancellationToken)
    {
        // --- Step 1: Fetch Order and related User Configuration ---
        var order = await dbContext.Orders
            .AsTracking()
            .Where(o => o.Id == request.OrderId)
            .Where(o => o.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "کد درخواست نامعتبر می‌باشد");

        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست افزودن تسویه امکان پذیر نیست");

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null) return Error.NotFound(description: "اطلاعات کاربری یافت نشد");

        // --- Step 2: Validate IBAN and get Payee's full name ---
        var fullName = await ibanInquiryService.TryValidateIbanAndAccountStatus(request.Iban);
        if (fullName.IsError)
            return fullName.Errors;

        var selectedIban = Iban.Of(request.Iban);

        // --- Step 3: Delegate transfer limit checks to the TransferLimitService ---
        var limitCheckResult = await transferLimitService.CheckDailyTransferLimitAsync(
            currentUserService.UserId,
            request.Amount,
            userConfig.DailyTransferLimit,
            cancellationToken);

        if (limitCheckResult.IsError)
            return limitCheckResult.Errors;

        // --- Step 4: Delegate Payee creation/retrieval to the PayeeService ---
        var payeeResult = await payeeService.GetOrCreatePayeeAsync(
            new PayeeInfo(request.NationalId, request.Mobile, selectedIban, fullName.Value),
            userConfig.Id,
            cancellationToken);

        if (payeeResult.IsError)
            return payeeResult.Errors;

        var payee = payeeResult.Value;

        // --- Step 5: Create and add the OrderDetail ---
        var orderDetail = OrderDetail.CreateFromPayee(
            userId: order.UserId,
            payee: payee,
            selectedIban: selectedIban,
            amount: request.Amount,
            wageAmount: wageCalculatorService.Calculate(userConfig, request.Amount),
            description: request.Description);

        order.AddDetail(orderDetail);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0
            ? new AddOrderDetailResponse(OrderDetailResponse.FromDomain(orderDetail))
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }
}

public sealed class AddOrderDetailCommandValidator : AbstractValidator<AddOrderDetailCommand>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;

    public AddOrderDetailCommandValidator(
        IApplicationDbContext dbContext,
        ICurrentUserService currentUserService,
        ITransferLimitService transferLimitService)
    {
        _dbContext = dbContext;
        _currentUserService = currentUserService;

        RuleFor(x => x.OrderId).NotEmpty();
        RuleFor(x => x.Iban).NotEmpty();
        RuleFor(x => x.Description).MaximumLength(50).WithMessage("متن توضیحات بیشتر از ۵۰ کارکتر نمی‌تواند باشد");
        RuleFor(x => x.Amount).NotEmpty().WithMessage("مبلغ اجباری است");

        RuleFor(x => x)
            .CustomAsync(async (command, context, ct) =>
            {
                var validationData = await GetValidationDataAsync(command, ct);

                if (validationData is null)
                {
                    context.AddFailure("User or Order not found.");
                    return;
                }

                // --- Rule 1: User must have Pro planned ---
                if (validationData.PlanType != SettlementPlanType.Pro)
                {
                    context.AddFailure(nameof(command.OrderId), ErrorMessages.SimplePlanSettlementAdditionForbidden);
                }

                // --- Rule 2: Check maximum details per order ---
                if (validationData.DetailsCount >= _currentUserService.GetMaxSettlementCountPerRequest)
                {
                    context.AddFailure(nameof(command.OrderId), string.Format(
                        ErrorMessages.MessageFormats.MaximumSettlementCountPerRequestExceeded,
                        _currentUserService.GetMaxSettlementCountPerRequest));
                }

                // --- Rule 3: Check amount boundaries ---
                var maxSettlementAmount = validationData.MaxSettlementAmount == 0
                    ? _currentUserService.GetMaxSettlementAmount
                    : validationData.MaxSettlementAmount;

                if (command.Amount < _currentUserService.GetMinSettlementAmount)
                {
                    context.AddFailure(nameof(command.Amount), "مبلغ تسویه کمتر از حد مجاز");
                }
                if (command.Amount > maxSettlementAmount)
                {
                    context.AddFailure(nameof(command.Amount), "مبلغ تسویه بیشتر از حد مجاز");
                }

                // --- Rule 4: Conditional rules for "Critical" users ---
                if (validationData.IsCritical)
                {
                    if (string.IsNullOrEmpty(command.Mobile) || !command.Mobile.IsValidIranianMobileNumber())
                    {
                        context.AddFailure(nameof(command.Mobile), ErrorMessages.InvalidMobileNumber);
                    }
                    if (string.IsNullOrEmpty(command.NationalId) || !command.NationalId.IsValidIranianNationalCode())
                    {
                        context.AddFailure(nameof(command.NationalId), ErrorMessages.InvalidNationalCode);
                    }

                    var limitCheckResult = await transferLimitService.CheckIbanTransferLimitAsync(
                        _currentUserService.UserId,
                        command.Iban,
                        command.Amount,
                        limit: _currentUserService.GetMaxLast24Amount, ct);

                    if (limitCheckResult.IsError)
                    {
                        context.AddFailure(nameof(command.Iban), limitCheckResult.FirstError.Description);
                    }
                }
            });
    }

    private sealed class UserOrderValidationData
    {
        public SettlementPlanType PlanType { get; init; }
        public bool IsCritical { get; init; }
        public long MaxSettlementAmount { get; init; }
        public int DetailsCount { get; init; }
    }

    private async Task<UserOrderValidationData?> GetValidationDataAsync(AddOrderDetailCommand command, CancellationToken ct)
    {
        return await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(u => u.UserId == _currentUserService.UserId)
            .Select(u => new UserOrderValidationData
            {
                PlanType = u.PlanType,
                IsCritical = u.IsCritical,
                MaxSettlementAmount = u.MaxSettlementAmount,
                DetailsCount = _dbContext.OrderDetails.Count(od => od.OrderId == command.OrderId)
            })
            .FirstOrDefaultAsync(ct);
    }
}
