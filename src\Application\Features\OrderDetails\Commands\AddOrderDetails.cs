using DispatchR.Requests.Send;
using DNTPersianUtils.Core;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.InternalServices;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.OrderDetails.Commands;

public sealed record AddOrderDetailsResponse(List<OrderDetailResponse> OrderDetails);

public sealed class AddOrderDetailsController : ApiControllerBase
{
    [HttpPost("{orderId:guid}/add-order-details")]
    [Authorize("write")]
    [ProducesResponseType<AddOrderDetailsResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddOrderDetails(
        [FromRoute] Guid orderId,
        [FromBody] AddOrderDetailsCommand request)
    {
        request.OrderId = orderId;
        var result = await Mediator.Send(request, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record AddOrderDetailsCommand(List<OrderDetailItem> Items)
    : IRequest<AddOrderDetailsCommand, Task<ErrorOr<AddOrderDetailsResponse>>>
{
    [JsonIgnore]
    public Guid OrderId { get; set; }
}

public sealed record OrderDetailItem(
    decimal Amount,
    string Iban,
    string NationalId,
    string Mobile,
    string? Description);

public sealed class AddOrderDetailsCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IWageCalculatorService wageCalculatorService,
    IIbanInquiryService ibanInquiryService,
    IPayeeService payeeService,
    ITransferLimitService transferLimitService)
    : IRequestHandler<AddOrderDetailsCommand, Task<ErrorOr<AddOrderDetailsResponse>>>
{
    public async Task<ErrorOr<AddOrderDetailsResponse>> Handle(
        AddOrderDetailsCommand request,
        CancellationToken cancellationToken)
    {
        // --- Step 1: Fetch Order and User Configuration ---
        var order = await dbContext.Orders
            .AsTracking()
            .Where(o => o.Id == request.OrderId)
            .Where(o => o.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "کد درخواست نامعتبر می‌باشد");

        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست افزودن تسویه امکان پذیر نیست");

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null) return Error.NotFound(description: "اطلاعات کاربری یافت نشد");

        // --- Step 2: Check overall daily transfer limit for the entire batch ---
        var totalAmount = request.Items.Sum(x => x.Amount);
        var dailyLimitResult = await transferLimitService.CheckDailyTransferLimitAsync(
            currentUserService.UserId,
            totalAmount,
            userConfig.DailyTransferLimit,
            cancellationToken);

        if (dailyLimitResult.IsError)
            return dailyLimitResult.Errors;

        // --- Step 3: Validate all IBANs in a single batch call ---
        var ibansToValidate = request.Items.Select(i => i.Iban).Distinct().ToArray();
        var inquiryResult = await ibanInquiryService.TryValidateIbanAndAccountStatus(ibansToValidate);
        if (inquiryResult.IsError)
            return inquiryResult.Errors;

        var validIbans = inquiryResult.Value;

        // --- Step 4: Process each item to create OrderDetail entities ---
        var createdOrderDetails = new List<OrderDetailResponse>();
        foreach (var item in request.Items)
        {
            var selectedIban = Iban.Of(item.Iban);
            var fullName = validIbans.GetValueOrDefault(selectedIban);

            var payeeResult = await payeeService.GetOrCreatePayeeAsync(
                new PayeeInfo(item.NationalId, item.Mobile, selectedIban, fullName),
                userConfig.Id,
                cancellationToken);

            if (payeeResult.IsError)
                return payeeResult.Errors;

            var payee = payeeResult.Value;

            var orderDetail = OrderDetail.CreateFromPayee(
                userId: order.UserId,
                payee: payee,
                selectedIban: selectedIban,
                amount: item.Amount,
                wageAmount: wageCalculatorService.Calculate(userConfig, item.Amount),
                description: item.Description);

            order.AddDetail(orderDetail);
            createdOrderDetails.Add(OrderDetailResponse.FromDomain(orderDetail));
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0
            ? new AddOrderDetailsResponse(createdOrderDetails)
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }
}

public sealed class AddOrderDetailsCommandValidator : AbstractValidator<AddOrderDetailsCommand>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;
    private readonly IDateTime _dateTime;

    public AddOrderDetailsCommandValidator(
        IApplicationDbContext dbContext,
        ICurrentUserService currentUserService,
        IDateTime dateTime)
    {
        _dbContext = dbContext;
        _currentUserService = currentUserService;
        _dateTime = dateTime;

        RuleFor(x => x.Items).NotEmpty().WithMessage("لیست آیتم‌ها نمی‌تواند خالی باشد");
        RuleFor(x => x.OrderId).NotEmpty();

        RuleFor(x => x)
            .CustomAsync(async (command, context, ct) =>
            {
                // Fetch all data required for validation in one go.
                var validationData = await GetValidationDataAsync(command, ct);
                if (validationData is null)
                {
                    context.AddFailure("User or Order not found.");
                    return;
                }

                // --- Rule 1: Check plan type ---
                if (validationData.PlanType != SettlementPlanType.Pro)
                {
                    context.AddFailure(ErrorMessages.SimplePlanSettlementAdditionForbidden);
                }

                // --- Rule 2: Check total count of items ---
                var maxItems = _currentUserService.GetMaxSettlementCountPerRequest;
                if (validationData.ExistingDetailsCount + command.Items.Count > maxItems)
                {
                    context.AddFailure(string.Format(
                        ErrorMessages.MessageFormats.MaximumSettlementCountPerRequestExceeded, maxItems));
                }

                // --- Rule 3: Validate each item in the list ---
                var maxSettlementAmount = validationData.MaxSettlementAmount == 0
                    ? _currentUserService.GetMaxSettlementAmount
                    : validationData.MaxSettlementAmount;

                for (var i = 0; i < command.Items.Count; i++)
                {
                    var item = command.Items[i];
                    var itemPrefix = $"{nameof(command.Items)}[{i}]";

                    // Amount checks
                    if (item.Amount < _currentUserService.GetMinSettlementAmount)
                        context.AddFailure(itemPrefix + ".Amount", "مبلغ تسویه کمتر از حد مجاز");
                    if (item.Amount > maxSettlementAmount)
                        context.AddFailure(itemPrefix + ".Amount", "مبلغ تسویه بیشتر از حد مجاز");

                    // Description length
                    if (item.Description?.Length > 50)
                        context.AddFailure(itemPrefix + ".Description", "متن توضیحات بیشتر از ۵۰ کارکتر نمی‌تواند باشد");

                    // Conditional "Critical User" checks
                    if (!validationData.IsCritical) continue;

                    if (string.IsNullOrEmpty(item.Mobile) || !item.Mobile.IsValidIranianMobileNumber())
                        context.AddFailure(itemPrefix + ".Mobile", ErrorMessages.InvalidMobileNumber);

                    if (string.IsNullOrEmpty(item.NationalId) || !item.NationalId.IsValidIranianNationalCode())
                        context.AddFailure(itemPrefix + ".NationalId", ErrorMessages.InvalidNationalCode);

                    // Check the per-IBAN transfer limit using pre-fetched data.
                    validationData.IbanTotals.TryGetValue(Iban.Of(item.Iban), out var existingTotal);
                    if (existingTotal + item.Amount > _currentUserService.GetMaxLast24Amount)
                    {
                        context.AddFailure(itemPrefix + ".Iban", $"امکان واریز بیش از 100میلیون تومان در ۲۴ساعت برای این شبا {item.Iban} نمی‌باشد");
                    }
                }
            });
    }
    private async Task<BulkValidationData?> GetValidationDataAsync(AddOrderDetailsCommand command, CancellationToken ct)
    {
        var userId = _currentUserService.UserId;

        var userConfigData = await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(u => u.UserId == userId)
            .Select(u => new
            {
                u.PlanType,
                u.IsCritical,
                u.MaxSettlementAmount
            }).FirstOrDefaultAsync(ct);

        if (userConfigData is null)
        {
            return null; // User not found, fail fast.
        }

        var existingDetailsCount = await _dbContext.OrderDetails
            .AsNoTracking()
            .CountAsync(od => od.OrderId == command.OrderId, ct);

        // Get the sum of transfers for each relevant IBAN for today.
        var todayStart = _dateTime.Now.Date;
        var tomorrowStart = todayStart.AddDays(1);
        var relevantIbans = command.Items.Select(i => i.Iban).Distinct().ToList();

        var ibanTotals = await _dbContext.OrderDetails
            .AsNoTracking()
            .Where(od => od.UserId == userId &&
                         od.Created >= todayStart && od.Created < tomorrowStart &&
                         relevantIbans.Contains(od.Iban))
            .GroupBy(od => od.Iban)
            .Select(g => new { Iban = g.Key, Total = g.Sum(od => od.Amount) })
            .ToDictionaryAsync(
                x => x.Iban,
                x => x.Total, ct);

        return new BulkValidationData
        {
            PlanType = userConfigData.PlanType,
            IsCritical = userConfigData.IsCritical,
            MaxSettlementAmount = userConfigData.MaxSettlementAmount,
            ExistingDetailsCount = existingDetailsCount,
            IbanTotals = ibanTotals
        };
    }

    private sealed class BulkValidationData
    {
        public SettlementPlanType PlanType { get; init; }
        public bool IsCritical { get; init; }
        public long MaxSettlementAmount { get; init; }
        public int ExistingDetailsCount { get; init; }
        public Dictionary<Iban, decimal> IbanTotals { get; init; } = [];
    }
}
