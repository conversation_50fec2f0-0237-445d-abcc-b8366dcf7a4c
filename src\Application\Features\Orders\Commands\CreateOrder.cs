﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders.Commands;

public sealed record CreateOrderResponse(Guid OrderId);
public sealed record CreateOrderRequest(
    string Title,
    string? Description);

public sealed class CreateOrderController : ApiControllerBase
{
    /// <summary>
    /// Handles the creation of a new order.
    /// </summary>
    /// <param name="request">
    /// The request containing the details of the order to be created, including the title and optional description.
    /// </param>
    /// <returns>
    /// An <see cref="IActionResult"/> containing the result of the operation:
    /// - A <see cref="CreateOrderResponse"/> with the ID of the created order if successful.
    /// - A <see cref="ProblemDetails"/> object if the request is invalid or an error occurs.
    /// </returns>
    /// <remarks>
    /// This endpoint requires authorization with the "write" policy.
    /// </remarks>
    [HttpPost("create-order")]
    [Authorize("write")]
    [ProducesResponseType<CreateOrderResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateOrder([FromBody] CreateOrderRequest request)
    {
        var command = new CreateOrderCommand(request);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}


public sealed record CreateOrderCommand(CreateOrderRequest Request)
    : IRequest<CreateOrderCommand, Task<ErrorOr<CreateOrderResponse>>>;

public sealed class CreateOrderCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<CreateOrderCommand, Task<ErrorOr<CreateOrderResponse>>>
{
    public async Task<ErrorOr<CreateOrderResponse>> Handle(
        CreateOrderCommand request,
        CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser == null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        if (await IsBannedUser(currentUser.Value, cancellationToken))
        {
            return Error.Validation(
                code: "UserBanned",
                description: "متاسفانه شما دسترسی ثبت درخواست تسویه ندارید. با پشتیبانی در ارتباط باشید");
        }

        var order = Order.Create(userId: currentUser.Value, request.Request.Title, request.Request.Description);

        dbContext.Orders.Add(order);
        var result = await dbContext.SaveChangesAsync(cancellationToken);

        return result > 0 ? new CreateOrderResponse(order.Id) : Error.Failure(description: "خطا در ثبت اطلاعات");
    }

    private Task<bool> IsBannedUser(int userId, CancellationToken cancellationToken)
    {
        return dbContext.UserConfigs
            .AsNoTracking()
            .Where(u => u.UserId == userId)
            .Select(uc => uc.IsBanned)
            .FirstOrDefaultAsync(cancellationToken);
    }
}

public sealed class CreateOrderCommandValidator : AbstractValidator<CreateOrderCommand>
{
    public CreateOrderCommandValidator()
    {
        RuleFor(x => x.Request.Title)
            .NotEmpty().WithMessage("عنوان درخواست اجباری می‌باشد.")
            .MaximumLength(100).WithMessage("عنوان درخواست بیشتر از 100 کارکتر نمی‌تواند باشد.");

        RuleFor(x => x.Request.Description)
            .MaximumLength(100).WithMessage("متن توضیحات بیشتر از ۵۰ کارکتر نمی‌تواند باشد.")
            .Must(IsValid).WithMessage(ErrorMessages.InvalidDescription);
    }

    private static bool IsValid(object? value)
    {
        if (value == null) return true;

        var word = value.ToString();

        return string.IsNullOrEmpty(word) ||
               Regex.IsMatch(word, @"^[\ a-zA-Z0-9\u0600-\u06FF]+$", RegexOptions.IgnoreCase);
    }
}