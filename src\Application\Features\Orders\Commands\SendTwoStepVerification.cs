﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders.Commands;

public sealed class SendTwoStepVerificationController : ApiControllerBase
{
    /// <summary>
    /// Sends a two-step verification request for the specified order.
    /// </summary>
    /// <param name="orderId">The unique identifier of the order for which the two-step verification will be sent.</param>
    /// <returns>Returns an <see cref="IActionResult"/> indicating the result of the operation.
    /// A 200 OK status is returned if successful; otherwise, a 400 Bad Request status is returned in case of validation errors or issues.</returns>
    [HttpPost("{orderId:guid}/verification")]
    [Authorize("write")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendTwoStepVerification([FromRoute] Guid orderId)
    {
        var result = await Mediator.Send(new SendTwoStepVerificationCommand(orderId), HttpContext.RequestAborted);
        return result.Match(_ => Ok(), Problem);
    }
}

public sealed record SendTwoStepVerificationCommand(Guid OrderId)
    : IRequest<SendTwoStepVerificationCommand, Task<ErrorOr<Success>>>;

public sealed class SendTwoStepVerificationCommandHandler(
    IApplicationDbContext dbContext,
    IEventServiceWrapper eventServiceWrapper,
    ICurrentUserService currentUserService)
    : IRequestHandler<SendTwoStepVerificationCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(SendTwoStepVerificationCommand request, CancellationToken cancellationToken)
    {
        if (currentUserService.UserId == null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        var totalAmount = await dbContext.OrderDetails
            .Where(x => x.OrderId == request.OrderId)
            .SumAsync(x => x.Amount, cancellationToken);

        await eventServiceWrapper.SendSettlementTwoFactorVerificationCodeAsync(
            currentUserService.UserId.Value,
            request.OrderId,
            totalAmount,
            cancellationToken);

        return Result.Success;
    }

}

public sealed class SendTwoStepVerificationCommandValidator : AbstractValidator<SendTwoStepVerificationCommand>
{
    public SendTwoStepVerificationCommandValidator(
        IApplicationDbContext dbContext,
        IRedisCacheService cacheService,
        ICurrentUserService currentUserService)
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("مقدار کد درخواست اجباری می‌باشد")
            .MustAsync(async (guid, token) =>
                string.IsNullOrWhiteSpace(await cacheService.GetAsync<string>(guid.ToString(), token)))
            .WithMessage("درخواست رمز تایید قبلا ارسال شده. بعد از دو دقیقه مجدد امتحان کنید")
            .MustAsync(async (guid, token) =>
            {
                var orderStatus = await dbContext.Orders
                    .Where(x => x.Id == guid)
                    .Where(o => o.UserId == currentUserService.UserId)
                    .Select(x => x.Status)
                    .FirstOrDefaultAsync(token);

                return orderStatus == OrderStatus.Draft;
            }).WithMessage("کد درخواست نامعتبر می‌باشد");
    }
}
