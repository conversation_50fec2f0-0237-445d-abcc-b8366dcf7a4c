using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;

using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders.Queries;

public sealed class GetOrderController : ApiControllerBase
{
    /// <summary>
    /// Retrieves a specific order by ID with all its details
    /// </summary>
    /// <param name="orderId">The unique identifier of the order</param>
    /// <returns>Order details including all order items</returns>
    [HttpGet("orders/{orderId:guid}")]
    [Authorize("read")]
    [ProducesResponseType<GetOrderResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetOrder(Guid orderId)
    {
        var query = new GetOrderQuery(orderId);
        var result = await Mediator.Send(query, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record GetOrderQuery(Guid OrderId)
    : IRequest<GetOrderQuery, Task<ErrorOr<GetOrderResponse>>>;

public sealed record GetOrderResponse(
    Guid Id,
    string OrderNumber,
    string Title,
    string Description,
    OrderStatus Status,
    decimal TotalAmount,
    decimal TotalWageAmount,
    int UserId,
    DateTimeOffset? ScheduledTime,
    DateTimeOffset? SubmittedTime,
    DateTimeOffset Created,
    DateTimeOffset? LastModified,
    List<GetOrderDetailResponse> OrderDetails)
{
    public static Expression<Func<Order, GetOrderResponse>> OrderSelector() =>
        order => new GetOrderResponse(
            order.Id,
            order.OrderNumber,
            order.Title,
            order.Description,
            order.Status,
            order.TotalAmount,
            order.TotalWageAmount,
            order.UserId,
            order.ScheduledTime,
            order.SubmittedTime,
            order.Created,
            order.LastModified,
            order.OrderDetails.Select(od => new GetOrderDetailResponse(
                od.Id,
                od.Description,
                od.Status,
                od.Amount,
                od.WageAmount,
                od.Iban.Value,
                od.Mobile,
                od.NationalId,
                od.FullName,
                od.PayeeId,
                od.Created,
                od.LastModified
            )).ToList()
        );
}

public sealed record GetOrderDetailResponse(
    Guid Id,
    string Description,
    OrderDetailStatus Status,
    decimal Amount,
    decimal WageAmount,
    string Iban,
    string? Mobile,
    string? NationalId,
    string? FullName,
    int? PayeeId,
    DateTimeOffset Created,
    DateTimeOffset? LastModified);

public sealed class GetOrderQueryHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<GetOrderQuery, Task<ErrorOr<GetOrderResponse>>>
{
    public async Task<ErrorOr<GetOrderResponse>> Handle(
        GetOrderQuery request,
        CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser == null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        var order = await dbContext.Orders
            .AsNoTracking()
            .Include(o => o.OrderDetails)
            .Where(o => o.Id == request.OrderId)
            .Where(o => o.UserId == currentUser.Value)
            .Select(GetOrderResponse.OrderSelector())
            .FirstOrDefaultAsync(cancellationToken);

        if (order == null)
            return Error.NotFound(description: "سفارش مورد نظر یافت نشد.");

        return order;
    }
}

public sealed class GetOrderQueryValidator : AbstractValidator<GetOrderQuery>
{
    public GetOrderQueryValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty()
            .WithMessage("شناسه سفارش نمی‌تواند خالی باشد.")
            .Must(BeValidGuid)
            .WithMessage("شناسه سفارش باید یک GUID معتبر باشد.");
    }

    private static bool BeValidGuid(Guid id) => id != Guid.Empty;
}