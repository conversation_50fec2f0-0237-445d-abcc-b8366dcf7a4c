﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public class AddFavoriteIbanController : ApiControllerBase
{
    [HttpPatch("users/add-favorite-iban")]
    [Authorize("write")]
    [ProducesResponseType<Updated>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddFavoriteIbanAsync([FromForm] AddFavoriteIbanCommand command)
    {
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(id => Ok(id), Problem);
    }
}

public sealed record AddFavoriteIbanCommand(
    string Iban,
    string? Alias = null)
    : IRequest<AddFavoriteIbanCommand, Task<ErrorOr<Updated>>>;

public sealed class AddFavoriteIbanCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<AddFavoriteIbanCommand, Task<ErrorOr<Updated>>>
{
    public async Task<ErrorOr<Updated>> Handle(AddFavoriteIbanCommand request, CancellationToken cancellationToken)
    {
        var favoriteIban = Iban.Of(request.Iban);
        var userConfiguration = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfiguration == null)
            return Error.NotFound(description: "User configuration not found.");

        var payee = await dbContext.Payees
            .AsNoTracking()
            .Include(p => p.Ibans)
            .Where(p => p.Ibans.Any(pi => pi.Iban == favoriteIban))
            .FirstOrDefaultAsync(cancellationToken);

        if (payee == null)
            return Error.NotFound(description: "No payee found with the specified IBAN.");

        var payeeUserConfig = await dbContext.PayeeUserConfigs
            .AsTracking()
            .Include(puc => puc.FavoriteIbans)
            .FirstOrDefaultAsync(puc => puc.PayeeId == payee.Id &&
                                        puc.UserConfigId == userConfiguration.Id,
                cancellationToken);

        if (payeeUserConfig == null)
        {
            // Create new relationship if it doesn't exist
            payeeUserConfig = PayeeUserConfig.Create(payee.Id, userConfiguration.Id);
            dbContext.PayeeUserConfigs.Add(payeeUserConfig);
        }

        // Add favorite IBAN
        payeeUserConfig.AddFavoriteIban(favoriteIban, request.Alias);

        await dbContext.SaveChangesAsync(cancellationToken);
        return new Updated();
    }
}
