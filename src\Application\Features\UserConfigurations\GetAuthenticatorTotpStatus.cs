﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public sealed record GetAuthenticatorTotpStatusResponse(bool AuthenticatorTotpEnabled);

public class GetAuthenticatorTotpStatusController : ApiControllerBase
{
    [HttpGet("users/authenticator-totp-status")]
    [Authorize("read")]
    [ProducesResponseType<GetAuthenticatorTotpStatusResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetAuthenticatorTotpStatusAsync()
    {
        var result =
            await Mediator.Send(new GetAuthenticatorTotpStatusQuery(), HttpContext.RequestAborted);

        return result.Match(Ok, Problem);
    }
}

public sealed class GetAuthenticatorTotpStatusQuery
    : IRequest<GetAuthenticatorTotpStatusQuery, Task<ErrorOr<GetAuthenticatorTotpStatusResponse>>>;

public sealed class GetAuthenticatorTotpStatusQueryHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService
    )
    : IRequestHandler<GetAuthenticatorTotpStatusQuery, Task<ErrorOr<GetAuthenticatorTotpStatusResponse>>>
{
    public async Task<ErrorOr<GetAuthenticatorTotpStatusResponse>> Handle(GetAuthenticatorTotpStatusQuery request, CancellationToken cancellationToken)
    {
        if (currentUserService.UserId is null or 0)
        {
            return new GetAuthenticatorTotpStatusResponse(false);
        }

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null)
        {
            return new GetAuthenticatorTotpStatusResponse(false);
        }

        var enabled = userConfig.AuthenticatorTotpEnabled &&
                      !string.IsNullOrWhiteSpace(userConfig.AuthenticatorTotpSecretKey);

        return new GetAuthenticatorTotpStatusResponse(enabled);
    }
}