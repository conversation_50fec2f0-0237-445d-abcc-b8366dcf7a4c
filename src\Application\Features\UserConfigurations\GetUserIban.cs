﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public sealed record GetUserIbanResponse(string? Iban);

public class GetUserIbanController : ApiControllerBase
{
    /// <summary>
    /// Retrieves the IBAN associated with the current user.
    /// </summary>
    /// <returns>
    /// An <see cref="GetUserIbanResponse"/> containing the user's IBAN if successful, or a problem response
    /// if an error occurs.
    /// </returns>
    /// <response code="200">Returns the user's IBAN in the response body.</response>
    /// <response code="400">Returns a problem response if the request is invalid or the user is not found.</response>
    [HttpGet("users/get-iban")]
    [Authorize("read")]
    [ProducesResponseType<GetUserIbanResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetIban()
    {
        var result = await Mediator.Send(new GetUserIbanQuery(), HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record GetUserIbanQuery
    : IRequest<GetUserIbanQuery, Task<ErrorOr<GetUserIbanResponse>>>;

public sealed class GetUserIbanQueryHandler(
    ICurrentUserService currentUserService,
    IApplicationDbContext dbContext)
    : IRequestHandler<GetUserIbanQuery, Task<ErrorOr<GetUserIbanResponse>>>
{
    public async Task<ErrorOr<GetUserIbanResponse>> Handle(GetUserIbanQuery request, CancellationToken cancellationToken)
    {
        var userId = currentUserService.UserId;
        if (userId == null)
            return (Error.Validation(description: "کاربر یافت نشد"));

        var iban = await dbContext.UserConfigs.AsNoTracking()
            .Where(x => x.UserId == userId)
            .Select(x => x.Iban)
            .FirstOrDefaultAsync(cancellationToken);

        return new GetUserIbanResponse(iban);
    }
}
