﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public class RemoveFavoriteIbanController : ApiControllerBase
{
    [HttpPatch("users/remove-favorite-iban")]
    [Authorize("write")]
    [ProducesResponseType<Updated>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RemoveFavoriteIbanAsync([FromForm] string iban)
    {
        var result = await Mediator.Send(new RemoveFavoriteIbanCommand(iban), HttpContext.RequestAborted);
        return result.Match(id => Ok(id), Problem);
    }
}

public sealed record RemoveFavoriteIbanCommand(string Iban)
    : IRequest<RemoveFavoriteIbanCommand, Task<ErrorOr<Updated>>>;

public sealed class RemoveFavoriteIbanCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<RemoveFavoriteIbanCommand, Task<ErrorOr<Updated>>>
{
    public async Task<ErrorOr<Updated>> Handle(RemoveFavoriteIbanCommand request, CancellationToken cancellationToken)
    {
        var favoriteIban = Iban.Of(request.Iban);
        var userConfiguration = await dbContext.UserConfigs
            .Where(x => x.UserId == currentUserService.UserId)
            .Select(x => new { x.Id })
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfiguration == null)
            return Error.NotFound(description: "User configuration not found.");

        var payee = await dbContext.Payees
            .Where(p => p.Ibans.Any(pi => pi.Iban == favoriteIban))
            .Select(x => new { x.Id })
            .FirstOrDefaultAsync(cancellationToken);

        if (payee == null)
            return Error.NotFound(description: "No payee found with the specified IBAN.");

        var payeeUserConfig = await dbContext.PayeeUserConfigs
            .AsTracking()
            .Include(puc => puc.FavoriteIbans)
            .FirstOrDefaultAsync(puc => puc.PayeeId == payee.Id &&
                                        puc.UserConfigId == userConfiguration.Id,
                cancellationToken);

        if (payeeUserConfig == null)
            return Error.NotFound(description: "relationship not found.");

        // Remove favorite IBAN
        payeeUserConfig.RemoveFavoriteIban(favoriteIban);

        await dbContext.SaveChangesAsync(cancellationToken);
        return new Updated();
    }
}