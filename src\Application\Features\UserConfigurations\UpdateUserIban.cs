﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public record UpdateUserIbanRequest(
    [Required(AllowEmptyStrings = false, ErrorMessage = "شماره شبا اجباری است")]
    [StringLength(26, MinimumLength = 26, ErrorMessage = "IBAN must be exactly 26 characters")]
    string Iban);

public class UpdateUserIbanController : ApiControllerBase
{
    /// <summary>
    /// Updates the user's IBAN. Users can only update their IBAN once per day.
    /// </summary>
    /// <param name="request">The request containing the new IBAN</param>
    /// <returns>Success if the IBAN was updated successfully</returns>
    [HttpPut("users/update-iban")]
    [Authorize("write")]
    [ProducesResponseType<Success>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UpdateUserIban([FromBody] UpdateUserIbanRequest request)
    {
        var command = new UpdateUserIbanCommand(request.Iban);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);

        return result.Match(id => Ok(id), Problem);
    }
}

public sealed record UpdateUserIbanCommand(string Iban)
    : IRequest<UpdateUserIbanCommand, Task<ErrorOr<Success>>>;

public sealed class UpdateUserIbanCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IDateTime dateTime,
    IInquiryApiClient inquiryApiClient)
    : IRequestHandler<UpdateUserIbanCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(UpdateUserIbanCommand request, CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .AsTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig == null)
            return Error.NotFound(description: "اطلاعات کاربری یافت نشد.");

        if (!userConfig.CanUpdateIban(dateTime.Now))
        {
            var maxChanges = userConfig.IbanUpdateMaxChangesPerWindow;
            var timeWindowHours = userConfig.IbanUpdateTimeWindowHours;

            return Error.Forbidden(description:
                $"شما در هر {timeWindowHours} ساعت تنها {maxChanges} بار می‌توانید شماره شبا خود را تغییر دهید. لطفاً بعداً تلاش کنید.");
        }

        var isShebaMatch = await inquiryApiClient.IsMatchingShebaWithNationalCode(
            sheba: request.Iban,
            nationalCode: /*user.NationalCode*/"", //TODO: get national code from user service
            birthDate: /*user.BirthDate*/"", //TODO: get birthdate from user service
            cancellationToken: cancellationToken);

        if (isShebaMatch.IsError) return isShebaMatch.Errors;
        if (!isShebaMatch.Value) return Error.Validation(description: "شماره شبا با اطلاعات هویتی شما مطابقت ندارد.");

        userConfig.UpdateIban(Iban.Of(request.Iban), dateTime.Now);
        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success;
    }
}

public sealed class UpdateUserIbanCommandValidator : AbstractValidator<UpdateUserIbanCommand>
{
    public UpdateUserIbanCommandValidator()
    {
        RuleFor(x => x.Iban)
            .NotEmpty().WithMessage("IBAN is required")
            .Must(iban => iban.StartsWith("IR")).WithMessage("IBAN must start with 'IR'")
            .Must(iban => iban[2..].All(char.IsDigit)).WithMessage("IBAN must contain only digits after 'IR'");
    }
}