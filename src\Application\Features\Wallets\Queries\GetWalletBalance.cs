﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Wallets.Queries;

public sealed record GetWalletBalanceResponse(decimal AccessBalance, decimal RestrictedBalance);

public sealed class GetWalletBalanceController : ApiControllerBase
{
    [HttpGet("wallet/get-balance")]
    [Authorize("read")]
    [ProducesResponseType<GetWalletBalanceResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetWalletBalanceAsync()
    {
        var result =
            await Mediator.Send(new GetWalletBalanceQuery(), HttpContext.RequestAborted);

        return result.Match(Ok, Problem);
    }
}

public sealed record GetWalletBalanceQuery
    : IRequest<GetWalletBalanceQuery, Task<ErrorOr<GetWalletBalanceResponse>>>;

public sealed class GetWalletBalanceQueryHandler(
    ICurrentUserService currentUserService,
    IApplicationDbContext dbContext,
    IWalletService walletService)
    : IRequestHandler<GetWalletBalanceQuery, Task<ErrorOr<GetWalletBalanceResponse>>>
{
    private static class ErrorMessages
    {
        public const string UserNotFound = "شناسه کاربر یافت نشد.";
        public const string SettlementWalletNotFound = "کیف پول تسویه کاربر یافت نشد.";
    }

    public async Task<ErrorOr<GetWalletBalanceResponse>> Handle(
        GetWalletBalanceQuery request,
        CancellationToken cancellationToken)
    {
        if (currentUserService.UserId is null or 0)
            return Error.NotFound(description: ErrorMessages.UserNotFound);

        var userWalletIds = await dbContext.UserWalletInformations
            .AsNoTracking()
            .Where(x => x.UserId == currentUserService.UserId)
            .Select(x => new { x.SettlementWalletId, x.PaymentWalletId })
            .FirstOrDefaultAsync(cancellationToken);

        if (userWalletIds?.SettlementWalletId is null)
            return Error.NotFound(description: ErrorMessages.SettlementWalletNotFound);

        var settlementWalletBalance = walletService.GetWalletAccessBalanceById(
            userWalletIds.SettlementWalletId.Value,
            cancellationToken);

        var paymentWalletBalance = walletService.GetWalletAccessBalanceById(
            userWalletIds.PaymentWalletId,
            cancellationToken);

        var walletBalances =
            await Task.WhenAll(settlementWalletBalance, paymentWalletBalance);

        var errors = walletBalances
            .Where(x => x.IsError)
            .SelectMany(x => x.Errors)
            .ToList();

        if (errors.Count != 0)
            return errors;

        var settlementBalance = walletBalances[0].Value; // Access Balance
        var paymentBalance = walletBalances[1].Value; // Restricted Balance

        return new GetWalletBalanceResponse(
            settlementBalance.AccessBalance,
            paymentBalance.AccessBalance);
    }
}
