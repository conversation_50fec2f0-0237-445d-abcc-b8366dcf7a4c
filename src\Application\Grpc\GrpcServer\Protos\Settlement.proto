syntax = "proto3";

option csharp_namespace = "Settlement";

import "Grpc/GrpcServer/Protos/BaseExceptionResponse.proto";

// SettlementGrpc service provides operations for settlement-related functionality
service SettlementGrpc {
  // Gets the settlement wallet ID for a specific user
  rpc GetSettlementWalletId (GetSettlementWalletIdRequest) returns (GetSettlementWalletIdResponse);
}

// ===== GetSettlementWalletId Messages =====

message GetSettlementWalletIdRequest {
  int32 user_id = 1; // Required user identifier.
}

message GetSettlementWalletIdResponse {
  // The response will contain either a wallet ID on success or a problem detail on failure.
  oneof result {
    string settlement_wallet_id = 1; // Settlement wallet ID (GUID as string) on success.
    baseExceptionResponse.ProblemDetail problem_detail = 2; // Error details if the operation failed.
  }
}