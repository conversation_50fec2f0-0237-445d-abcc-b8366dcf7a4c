syntax = "proto3";

option csharp_namespace = "Currency.Coin";

import "Grpc/Protos/BaseExceptionResponse.proto";

service CoinGrpc {
   rpc GetCoins (GetCoinsRequest) returns (GetCoinsResponse);
}

/* GET COINS */
message GetCoinsRequest {  
}

message GetCoinsResponse {
  repeated CoinDetail coins = 1;
  baseExceptionResponse.ProblemDetail problemDetail = 2;
}

message CoinDetail {
  string id = 1;
  string name = 2;
  string symbol = 3;
}