﻿using ErrorOr;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp.Models;

namespace Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp;

public class InquiryApiClient(
    HttpClient httpClient,
    ILogger<InquiryApiClient> logger) : IInquiryApiClient
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    public Task<ErrorOr<bool>> IsMatchingShebaWithNationalCode(
        string sheba,
        string nationalCode,
        string birthDate,
        CancellationToken cancellationToken = default)
    {
        var formattedBirthDate = birthDate.Replace("/", "").Replace("-", "").Replace(".", "");

        var queryParams = new Dictionary<string, string?>
        {
            { "sheba", sheba },
            { "nationalCode", nationalCode },
            { "birthDate", formattedBirthDate }
        };

        return GetMatchResultAsync(Endpoints.NationalCodeWithSheba, queryParams, cancellationToken);
    }

    public Task<ErrorOr<bool>> IsMatchingMobileWithNationalCode(
        string mobileNumber,
        string nationalCode,
        CancellationToken cancellationToken = default)
    {
        var queryParams = new Dictionary<string, string?>
        {
            { "mobileNumber", mobileNumber },
            { "nationalCode", nationalCode }
        };

        return GetMatchResultAsync(Endpoints.NationalCodeWithMobile, queryParams, cancellationToken);
    }

    #region Private Methods
    private async Task<ErrorOr<bool>> GetMatchResultAsync(
    string endpoint,
    Dictionary<string, string?> queryParams,
    CancellationToken cancellationToken)
    {
        if (httpClient.BaseAddress == null)
        {
            logger.LogError("HttpClient BaseAddress is not configured");
            return Error.Failure(ErrorCodes.Configuration, "خطا در تنظیمات سرویس");
        }

        var uri = new Uri(httpClient.BaseAddress, endpoint);
        var url = QueryHelpers.AddQueryString(uri.ToString(), queryParams);
        var loggableParams = string.Join(", ", queryParams.Select(kvp => $"{kvp.Key}: {kvp.Value}"));

        try
        {
            logger.LogDebug("Making request to Inquiry API. Endpoint: {Endpoint}, Params: [{LoggableParams}]",
                endpoint, loggableParams);

            var httpResponse = await httpClient.GetAsync(url, cancellationToken);

            if (httpResponse.IsSuccessStatusCode)
            {
                return await HandleSuccessResponse(httpResponse, loggableParams, cancellationToken);
            }

            return await HandleErrorResponse(httpResponse, loggableParams, cancellationToken);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            logger.LogError(ex, "Timeout occurred while calling Inquiry API. Params: [{LoggableParams}]",
                loggableParams);
            return Error.Failure(ErrorCodes.Timeout, "زمان انتظار برای دریافت پاسخ به پایان رسید");
        }
        catch (TaskCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            logger.LogInformation("Inquiry API request was cancelled. Params: [{LoggableParams}]", loggableParams);
            return Error.Failure(ErrorCodes.Cancelled, "درخواست لغو شد");
        }
        catch (HttpRequestException ex)
        {
            logger.LogError(ex, "Network error occurred while calling Inquiry API. Params: [{LoggableParams}]",
                loggableParams);
            return Error.Failure(ErrorCodes.NetworkError, "خطا در برقراری ارتباط با سرویس استعلام");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error occurred while calling Inquiry API. Params: [{LoggableParams}]",
                loggableParams);
            return Error.Failure(ErrorCodes.UnexpectedError, "خطای غیرمنتظره در سرویس استعلام");
        }
    }

    private async Task<ErrorOr<bool>> HandleSuccessResponse(
        HttpResponseMessage httpResponse,
        string loggableParams,
        CancellationToken cancellationToken)
    {
        try
        {
            var response = await httpResponse.Content.ReadFromJsonAsync<MatchingResponseModel>(
                JsonSerializerOptions,
                cancellationToken: cancellationToken);

            if (response != null)
            {
                logger.LogDebug(
                    "Successfully received response from Inquiry API. Matched: {Matched}, Params: [{LoggableParams}]",
                    response.Matched, loggableParams);
                return response.Matched;
            }

            logger.LogWarning(
                "Inquiry API returned successful status but response body was null. Params: [{LoggableParams}]",
                loggableParams);
            return Error.Failure(ErrorCodes.NullResponse, "پاسخ دریافت شده از سرویس استعلام معتبر نیست");
        }
        catch (JsonException ex)
        {
            logger.LogWarning(ex, "Failed to deserialize JSON response from Inquiry API. Params: [{LoggableParams}]",
                loggableParams);
            return Error.Failure(ErrorCodes.DeserializationError, "پاسخ دریافت شده از سرویس استعلام معتبر نیست");
        }
    }

    private async Task<ErrorOr<bool>> HandleErrorResponse(
        HttpResponseMessage httpResponse,
        string loggableParams,
        CancellationToken cancellationToken)
    {
        var message = await httpResponse.Content.ReadAsStringAsync(cancellationToken);

        var error = httpResponse.StatusCode switch
        {
            HttpStatusCode.NotFound => Error.NotFound(ErrorCodes.NotFound, "اطلاعات یافت نشد"),
            HttpStatusCode.BadRequest => Error.Validation(ErrorCodes.BadRequest, "درخواست نامعتبر"),
            HttpStatusCode.Unauthorized => Error.Unauthorized(ErrorCodes.Unauthorized, "عدم دسترسی به سرویس استعلام"),
            HttpStatusCode.Forbidden => Error.Forbidden(ErrorCodes.Forbidden, "دسترسی به سرویس استعلام مجاز نیست"),
            HttpStatusCode.TooManyRequests => Error.Failure(ErrorCodes.RateLimited, "تعداد درخواست‌ها از حد مجاز فراتر رفته است"),
            HttpStatusCode.InternalServerError => Error.Failure(ErrorCodes.ServerError, "خطای داخلی سرویس استعلام"),
            HttpStatusCode.ServiceUnavailable => Error.Failure(ErrorCodes.ServiceUnavailable, "سرویس استعلام در دسترس نیست"),
            _ => Error.Failure(ErrorCodes.RequestFailed, "خطا در استعلام")
        };

        logger.LogError(
            "Inquiry API request failed. Status: {StatusCode}, Message: {Message}, Params: [{LoggableParams}]",
            httpResponse.StatusCode, message, loggableParams);

        return error;
    }
    #endregion

    #region Constants
    private static class Endpoints
    {
        public const string NationalCodeWithSheba = "v1/Matching/nationalCode-with-sheba";
        public const string NationalCodeWithMobile = "v1/Matching/nationalcode-with-mobile";
    }

    private static class ErrorCodes
    {
        public const string Configuration = "InquiryApi.Configuration";
        public const string DeserializationError = "InquiryApi.DeserializationError";
        public const string NullResponse = "InquiryApi.NullResponse";
        public const string RequestFailed = "InquiryApi.RequestFailed";
        public const string NetworkError = "InquiryApi.NetworkError";
        public const string Timeout = "InquiryApi.Timeout";
        public const string Cancelled = "InquiryApi.Cancelled";
        public const string UnexpectedError = "InquiryApi.UnexpectedError";
        public const string NotFound = "InquiryApi.NotFound";
        public const string BadRequest = "InquiryApi.BadRequest";
        public const string Unauthorized = "InquiryApi.Unauthorized";
        public const string Forbidden = "InquiryApi.Forbidden";
        public const string RateLimited = "InquiryApi.RateLimited";
        public const string ServerError = "InquiryApi.ServerError";
        public const string ServiceUnavailable = "InquiryApi.ServiceUnavailable";
    }
    #endregion
}