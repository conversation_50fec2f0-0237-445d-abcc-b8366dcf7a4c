﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Unchase.Swashbuckle.AspNetCore.Extensions.Extensions;
using Zify.Settlement.Application.Infrastructure.Web;
using Zify.Settlement.Application.Infrastructure.Web.OpenApiFilters;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddCustomSwaggerExtension
{
    public static IServiceCollection AddCustomSwagger(this IServiceCollection services)
    {
        services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
        services.AddSwaggerGen(options =>
        {
            // Add the folder-based organization filters
            options.OperationFilter<FolderBasedTagsOperationFilter>();

            // https://github.com/unchase/Unchase.Swashbuckle.AspNetCore.Extensions#fix-enums
            options.AddEnumsWithValuesFixFilters(option =>
            {
                option.IncludeXEnumRemarks = true;
                option.IncludeDescriptions = true;
                option.DescriptionSource = DescriptionSources.DescriptionAttributes;

            });

            // JWT Bearer Authentication
            var bearerScheme = new OpenApiSecurityScheme()
            {
                Type = SecuritySchemeType.Http,
                Name = JwtBearerDefaults.AuthenticationScheme,
                Scheme = JwtBearerDefaults.AuthenticationScheme,
                Reference = new OpenApiReference
                {
                    Id = JwtBearerDefaults.AuthenticationScheme,
                    Type = ReferenceType.SecurityScheme,
                }
            };
            options.AddSecurityDefinition(JwtBearerDefaults.AuthenticationScheme, bearerScheme);
            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                { bearerScheme, [] }
            });

            // Handle conflicting actions (important for API versioning)
            options.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
            options.EnableAnnotations();

            // Include XML comments if available
            var apiXmlFile = Path.Combine(AppContext.BaseDirectory, "Zify.Settlement.Api.xml");
            if (File.Exists(apiXmlFile))
            {
                options.IncludeXmlComments(apiXmlFile, true);
            }

            var applicationXmlFile = Path.Combine(AppContext.BaseDirectory, "Zify.Settlement.Application.xml");
            if (File.Exists(applicationXmlFile))
            {
                options.IncludeXmlComments(applicationXmlFile, true);
            }
        });

        return services;
    }

    public static WebApplication UseCustomSwagger(this WebApplication app)
    {
        var basePath = Environment.GetEnvironmentVariable("REVERSE_PROXY_BASE_PATH") ?? "";

        // Enable middleware to serve generated Swagger as a JSON endpoint.
        app.UseSwagger();

        // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.), specifying the Swagger JSON endpoint.
        app.UseSwaggerUI(options =>
        {
            options.RoutePrefix = "docs";
            var swaggerJsonBasePath = string.IsNullOrWhiteSpace(basePath) ? "/swagger" : $"{basePath}/swagger";

            var descriptions = app.DescribeApiVersions();
            // build a swagger endpoint for each discovered API version
            foreach (var description in descriptions)
            {
                var url = $"{swaggerJsonBasePath}/{description.GroupName}/swagger.json";
                var name = description.GroupName.ToUpperInvariant();
                options.SwaggerEndpoint(url, name);
            }
        });

        return app;
    }
}
