using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Zify.Settlement.Application.Infrastructure.Services.Grpc;
using Zify.Settlement.Application.Infrastructure.Services.Grpc.Interceptors;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;

/// <summary>
/// Extension methods for configuring gRPC server services.
/// </summary>
public static class AddGrpcServerExtension
{
    /// <summary>
    /// Adds gRPC server services with proper configuration validation and error handling.
    /// </summary>
    public static IServiceCollection AddGrpcServer(
        this IServiceCollection services,
        IHostEnvironment environment)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(environment);

        // Add gRPC services
        services.AddGrpc(options =>
        {
            // Configure global gRPC options
            options.EnableDetailedErrors = environment.IsDevelopment();
            options.Interceptors.Add<ExceptionHandlerInterceptor>();
        });

        // Add gRPC reflection services
        services.AddGrpcReflection();

        return services;
    }

    /// <summary>
    /// Maps gRPC services to the application pipeline.
    /// </summary>
    public static WebApplication MapGrpcServices(this WebApplication app)
    {
        ArgumentNullException.ThrowIfNull(app);

        // Map gRPC services
        app.MapGrpcService<SettlementGrpcService>();

        // Enable gRPC reflection in development for tools like grpcurl or gRPC UI
        if (app.Environment.IsDevelopment())
        {
            app.MapGrpcReflectionService();
        }

        return app;
    }
}
