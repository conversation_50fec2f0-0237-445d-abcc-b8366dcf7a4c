﻿using Microsoft.Extensions.Configuration;

namespace Zify.Settlement.Application.Infrastructure.Configurations;

public sealed class RabbitMqOptions
{
    [Configuration<PERSON><PERSON><PERSON>ame("RabbitMQ_ConnectionString")]
    public string RabbitUri { get; set; } = null!;

    [Configuration<PERSON><PERSON><PERSON>ame("RabbitMqUsername")]
    public string RabbitUsername { get; set; } = null!;

    [Configuration<PERSON><PERSON>Name("RabbitMqPassword")]
    public string RabbitPassword { get; set; } = null!;
}