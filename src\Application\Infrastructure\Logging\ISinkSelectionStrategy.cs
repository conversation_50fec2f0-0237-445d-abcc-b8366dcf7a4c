using Serilog;
using Serilog.Sinks.Elasticsearch;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Logging;

/// <summary>
/// Defines the contract for sink selection strategies.
/// </summary>
public interface ISinkSelectionStrategy
{
    /// <summary>
    /// Configures the Serilog sinks based on the provided options.
    /// </summary>
    /// <param name="loggerConfiguration">The logger configuration to modify.</param>
    /// <param name="options">The configuration options.</param>
    /// <returns>The modified logger configuration.</returns>
    LoggerConfiguration ConfigureSinks(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options);
}

public abstract class BaseSinkStrategy : ISinkSelectionStrategy
{
    public virtual LoggerConfiguration ConfigureSinks(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options)
    {
        ArgumentNullException.ThrowIfNull(loggerConfiguration);
        ArgumentNullException.ThrowIfNull(options);

        try
        {
            // Configure Logstash sink if enabled
            if (options.LogstashEnabled)
            {
                loggerConfiguration = ConfigureLogstashSink(loggerConfiguration, options);
                return loggerConfiguration;
            }

            // Configure Elasticsearch sink if enabled
            if (options.ElasticsearchEnabled)
            {
                loggerConfiguration = ConfigureElasticsearchSink(loggerConfiguration, options);
            }

            return loggerConfiguration;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(
                $"Failed to configure sinks for transport type {options.LogstashType}: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Configures the Logstash sink. Must be implemented by derived classes.
    /// </summary>
    protected abstract LoggerConfiguration ConfigureLogstashSink(
        LoggerConfiguration loggerConfiguration,
        ElasticsearchOptions options);

    /// <summary>
    /// Configures the Elasticsearch sink with common settings.
    /// </summary>
    protected static LoggerConfiguration ConfigureElasticsearchSink(
        LoggerConfiguration loggerConfiguration,
        ElasticsearchOptions options)
    {
        var elasticsearchSinkOptions = new ElasticsearchSinkOptions(new Uri(options.Uri))
        {
            IndexFormat = $"{options.IndexName}-{{0:yyyy.MM.dd}}",
            AutoRegisterTemplate = options.AutoRegisterTemplate,
            NumberOfShards = options.NumberOfShards,
            NumberOfReplicas = options.NumberOfReplicas,
            BatchPostingLimit = options.BatchPostingLimit,
            Period = options.Period,
            ConnectionTimeout = TimeSpan.FromMilliseconds(options.ConnectionTimeout),
            DeadLetterIndexName = $"{options.IndexName}-deadletter",
            FailureCallback = (@event, exception) => Console.WriteLine($"Elasticsearch logging failed: {@event.MessageTemplate}"),
            EmitEventFailure = EmitEventFailureHandling.WriteToSelfLog | EmitEventFailureHandling.ThrowException
        };

        // Configure authentication if provided
        if (!string.IsNullOrWhiteSpace(options.Username) && !string.IsNullOrWhiteSpace(options.Password))
        {
            elasticsearchSinkOptions.ModifyConnectionSettings = connectionConfiguration =>
                connectionConfiguration.BasicAuthentication(options.Username, options.Password);
        }

        return loggerConfiguration.WriteTo.Elasticsearch(elasticsearchSinkOptions);
    }
}
