using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System.Reflection;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence;

public sealed class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
    : DbContext(options), IApplicationDbContext
{
    //private DbSet<OutboxEntity> Outboxes { get; set; } = null!;
    public DbSet<Order> Orders => Set<Order>();
    public DbSet<OrderDetail> OrderDetails => Set<OrderDetail>();
    public DbSet<OrderDetailRollbackInfo> OrderDetailRollbackInfos => Set<OrderDetailRollbackInfo>();
    public DbSet<UserConfig> UserConfigs => Set<UserConfig>();
    public DbSet<UserWalletInformation> UserWalletInformations => Set<UserWalletInformation>();
    public DbSet<Payee> Payees => Set<Payee>();
    public DbSet<PayeeUserConfig> PayeeUserConfigs => Set<PayeeUserConfig>();

    public Task<IDbContextTransaction> BeginTransactionAsync(
        CancellationToken cancellationToken = default) => Database.BeginTransactionAsync(cancellationToken);

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // Configure AuditableEntity properties for all derived entities
        var mutableEntityTypes = builder.Model
            .GetEntityTypes().Where(e => typeof(AuditableEntity).IsAssignableFrom(e.ClrType));
        foreach (var entityType in mutableEntityTypes)
        {
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.Version)).IsRowVersion();
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.Created)).IsRequired();
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.CreatedBy)).IsRequired(false);
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.LastModified)).IsRequired(false);
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.LastModifiedBy)).IsRequired(false);
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.IsDeleted)).IsRequired().HasDefaultValue(false);
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.DeletedOn)).IsRequired(false);
        }
    }
}
