﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;
public class OrderConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        builder.ToTable("Orders");

        builder.HasKey(o => o.Id);

        builder.Property(o => o.Id)
               .ValueGeneratedNever(); // Assuming ID is a Guid and set manually

        builder.Property(x => x.OrderNumber)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(o => o.UserId)
            .IsRequired();

        builder.Property(o => o.Title)
               .IsRequired()
               .HasMaxLength(200);

        builder.Property(o => o.Description)
               .IsRequired()
               .HasMaxLength(500);

        builder.Property(o => o.Status)
               .IsRequired();

        builder.Property(x => x.TotalAmount)
            .HasPrecision(24, 8)
            .IsRequired();

        builder.Property(x => x.TotalWageAmount)
            .HasPrecision(24, 8)
            .IsRequired();

        builder.Property(o => o.ScheduledTime)
               .IsRequired(false);

        builder.Property(o => o.SubmittedTime)
            .IsRequired(false);

        builder.HasMany(o => o.OrderDetails)
               .WithOne(o => o.Order)
               .HasForeignKey(od => od.OrderId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.Property(o => o.WalletBlockCorrelationId)
            .HasColumnName("WalletBlockCorrelationId")
            .HasConversion(
                v => v.HasValue ? v.Value.Value : (Guid?)null,
                v => v.HasValue ? WalletId.Of(v.Value) : null);

        builder.Property(o => o.WalletWithdrawCorrelationId)
            .HasColumnName("WalletWithdrawCorrelationId")
            .HasConversion(
                v => v.HasValue ? v.Value.Value : (Guid?)null,
                v => v.HasValue ? WalletId.Of(v.Value) : null);

        builder.HasIndex(x => x.OrderNumber)
            .IsUnique()
            .HasDatabaseName("IX_Orders_OrderNumber");

        builder.HasIndex(x => x.UserId)
            .HasDatabaseName("IX_Orders_UserId");

        builder.HasIndex(x => x.Status)
            .HasDatabaseName("IX_Orders_Status");

        builder.HasIndex(x => new { x.UserId, x.Status })
            .HasDatabaseName("IX_Orders_UserId_Status");

    }
}
