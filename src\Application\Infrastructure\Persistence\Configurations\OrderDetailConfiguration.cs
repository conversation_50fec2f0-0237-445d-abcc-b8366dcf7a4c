﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;
public class OrderDetailConfiguration : IEntityTypeConfiguration<OrderDetail>
{
    public void Configure(EntityTypeBuilder<OrderDetail> builder)
    {
        builder.ToTable("OrderDetails");

        builder.<PERSON>Key(od => od.Id);

        builder.Property(od => od.Id)
               .ValueGeneratedNever(); // Assuming ID is a Guid and set manually

        builder.Property(x => x.UserId)
            .IsRequired();

        builder.Property(od => od.Description)
               .IsRequired()
               .HasMaxLength(500);

        builder.Property(od => od.Status)
               .IsRequired();

        builder.Property(od => od.Iban)
            .HasConversion(
                iban => iban.Value,
                value => Iban.Of(value))
            .HasColumnName("Iban")
            .IsRequired();

        builder.Property(od => od.WageTransferTransactionId)
            .HasColumnName("WageTransferTransactionId")
            .HasConversion(
                v => v.HasValue ? v.Value.Value : (Guid?)null,
                v => v.HasValue ? CorrelationId.Of(v.Value) : null);

        builder.Property(od => od.Amount)
               .IsRequired()
               .HasPrecision(24, 8);

        builder.Property(od => od.WageAmount)
               .IsRequired()
               .HasPrecision(24, 8);

        builder.Property(od => od.Mobile)
               .HasMaxLength(15)
               .IsRequired(false);

        builder.Property(od => od.NationalId)
               .HasMaxLength(10)
               .IsRequired(false);

        builder.Property(od => od.FullName)
               .HasMaxLength(200)
               .IsRequired(false);

        builder.HasOne(od => od.Order)
               .WithMany(o => o.OrderDetails)
               .HasForeignKey(od => od.OrderId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(od => od.Payee)
            .WithMany()
            .HasForeignKey(od => od.PayeeId)
            .IsRequired(false) // Optional relationship
            .OnDelete(DeleteBehavior.SetNull); // If payee is deleted, keep the order detail

        builder.HasOne(od => od.OrderDetailRollbackInfo)
               .WithOne(ri => ri.OrderDetail)
               .HasForeignKey<OrderDetailRollbackInfo>(ri => ri.OrderDetailId)
               .OnDelete(DeleteBehavior.Restrict)
               .IsRequired(false);

        builder.HasIndex(od => od.OrderId)
            .HasDatabaseName("IX_OrderDetails_OrderId");

        builder.HasIndex(od => od.PayeeId)
            .HasDatabaseName("IX_OrderDetails_PayeeId");

        builder.HasIndex(od => od.Iban)
            .HasDatabaseName("IX_OrderDetails_Iban");

        builder.HasIndex(od => od.UserId)
            .HasDatabaseName("IX_OrderDetails_UserId");
    }
}
