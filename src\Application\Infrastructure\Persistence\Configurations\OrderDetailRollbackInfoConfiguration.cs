using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;

public class OrderDetailRollbackInfoConfiguration : IEntityTypeConfiguration<OrderDetailRollbackInfo>
{
    public void Configure(EntityTypeBuilder<OrderDetailRollbackInfo> builder)
    {
        builder.ToTable("OrderDetailRollbackInfos");

        builder.HasKey(o => o.Id);

        builder.Property(o => o.Id)
               .ValueGeneratedOnAdd();

        builder.Property(o => o.IsRollbackCompleted)
               .IsRequired();

        builder.Property(o => o.OrderDetailId)
               .IsRequired();


        builder.Property(x => x.RollbackFailureReason)
            .HasMaxLength(1000);

        builder.ComplexProperty(
               o => o.RollbackTransferTransactionId, buildAction =>
               {
                   buildAction.Property(p => p.Value)
                             .HasColumnName("RollbackTransferTransactionId")
                             .IsRequired();
               });

        builder.ComplexProperty(
               o => o.RollbackWageTransactionId, buildAction =>
               {
                   buildAction.Property(p => p.Value)
                             .HasColumnName("RollbackWageTransactionId")
                             .IsRequired();
               });

        builder.ComplexProperty(
               o => o.RollbackDestinationWalletId, buildAction =>
               {
                   buildAction.Property(p => p.Value)
                             .HasColumnName("RollbackDestinationWalletId")
                             .IsRequired();
               });

        builder.HasOne(o => o.OrderDetail)
               .WithOne(od => od.OrderDetailRollbackInfo)
               .HasForeignKey<OrderDetailRollbackInfo>(o => o.OrderDetailId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(x => x.OrderDetailId)
            .IsUnique()
            .HasDatabaseName("IX_OrderDetailRollbackInfos_OrderDetailId");

        builder.HasIndex(x => x.IsRollbackCompleted)
            .HasDatabaseName("IX_OrderDetailRollbackInfos_IsRollbackCompleted");

        builder.HasIndex(x => x.RollbackRequestedAt)
            .HasDatabaseName("IX_OrderDetailRollbackInfos_RollbackRequestedAt");
    }
}