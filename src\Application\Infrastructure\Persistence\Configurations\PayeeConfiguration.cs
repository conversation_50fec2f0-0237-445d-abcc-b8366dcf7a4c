﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;

public class PayeeConfiguration : IEntityTypeConfiguration<Payee>
{
    public void Configure(EntityTypeBuilder<Payee> builder)
    {
        builder.ToTable("Payees");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.NationalCode)
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(x => x.FullName)
            .HasMaxLength(200)
            .IsRequired(false);

        builder.HasIndex(x => x.NationalCode)
            .IsUnique()
            .HasDatabaseName("IX_Payees_NationalCode");

        // Configure mobile numbers as owned entities
        builder.OwnsMany(x => x.MobileNumbers, mb =>
        {
            mb.ToTable("PayeeMobileNumbers");
            mb.WithOwner().HasForeignKey(x => x.PayeeId);
            mb.<PERSON><PERSON>(x => new { x.PayeeId, x.MobileNumber });

            mb.Property(x => x.MobileNumber)
                .HasMaxLength(15)
                .IsRequired();

            mb.HasIndex(x => x.MobileNumber)
                .HasDatabaseName("IX_PayeeMobileNumbers_MobileNumber");
        });

        // Configure IBANs as owned entities
        builder.OwnsMany(x => x.Ibans, ib =>
        {
            ib.ToTable("PayeeIbans");
            ib.WithOwner().HasForeignKey(x => x.PayeeId);
            ib.HasKey(x => new { x.PayeeId, x.Iban });

            ib.Property(x => x.Iban)
                .HasMaxLength(34)
                .IsRequired();

            ib.HasIndex(x => x.Iban)
                .HasDatabaseName("IX_PayeeIbans_Iban");
        });

        builder.HasMany(x => x.PayeeUserConfigs)
            .WithOne(x => x.Payee)
            .HasForeignKey(x => x.PayeeId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}