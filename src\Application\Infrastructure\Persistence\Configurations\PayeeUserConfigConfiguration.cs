﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;

public class PayeeUserConfigConfiguration : IEntityTypeConfiguration<PayeeUserConfig>
{
    public void Configure(EntityTypeBuilder<PayeeUserConfig> builder)
    {
        builder.ToTable("PayeeUserConfigs");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.PayeeId)
            .IsRequired();

        builder.Property(x => x.UserConfigId)
            .IsRequired();

        builder.Property(x => x.IsBeneficiary)
            .HasDefaultValue(false);

        // Unique constraint to prevent duplicate relationships
        builder.HasIndex(x => new { x.PayeeId, x.UserConfigId })
            .IsUnique()
            .HasDatabaseName("IX_PayeeUserConfigs_PayeeId_UserConfigId");

        // Foreign key relationships
        builder.HasOne(x => x.Payee)
            .WithMany(x => x.PayeeUserConfigs)
            .HasForeignKey(x => x.PayeeId)
            .OnDelete(DeleteBehavior.Cascade);
        builder.Navigation(x => x.Payee).AutoInclude();

        builder.HasOne(x => x.UserConfig)
            .WithMany(x => x.PayeeUserConfigs)
            .HasForeignKey(x => x.UserConfigId)
            .OnDelete(DeleteBehavior.Cascade);
        builder.Navigation(x => x.UserConfig).AutoInclude();

        // Configure favorite IBANs as owned entities
        builder.OwnsMany(x => x.FavoriteIbans, fi =>
        {
            fi.ToTable("FavoriteIbans");

            fi.WithOwner()
                .HasForeignKey(x => x.PayeeUserConfigId);

            fi.HasKey(x => new { x.PayeeUserConfigId, x.Iban });

            fi.Property(x => x.Iban)
                .HasMaxLength(34)
                .IsRequired();

            fi.Property(x => x.Alias)
                .HasMaxLength(100);

            fi.HasIndex(x => x.Iban)
                .HasDatabaseName("IX_FavoriteIbans_Iban");
        });
    }
}