using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence;

public interface IApplicationDbContext
{
    DbSet<Order> Orders { get; }
    DbSet<OrderDetail> OrderDetails { get; }
    DbSet<OrderDetailRollbackInfo> OrderDetailRollbackInfos { get; }
    DbSet<UserConfig> UserConfigs { get; }
    DbSet<UserWalletInformation> UserWalletInformations { get; }
    public DbSet<Payee> Payees { get; }
    public DbSet<PayeeUserConfig> PayeeUserConfigs { get; }


    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
}