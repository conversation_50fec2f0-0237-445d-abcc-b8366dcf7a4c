﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Zify.Settlement.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddPayeeEntityAndRelatedEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FavoriteIbans",
                table: "UserConfigs");

            migrationBuilder.RenameColumn(
                name: "TransactionStatus",
                table: "OrderDetailRollbackInfos",
                newName: "IsRollbackCompleted");

            migrationBuilder.AddColumn<string>(
                name: "OrderNumber",
                table: "Orders",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "SubmittedTime",
                table: "Orders",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalAmount",
                table: "Orders",
                type: "numeric(24,8)",
                precision: 24,
                scale: 8,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalWageAmount",
                table: "Orders",
                type: "numeric(24,8)",
                precision: 24,
                scale: 8,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                table: "Orders",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "PayeeId",
                table: "OrderDetails",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                table: "OrderDetails",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "RollbackFailureReason",
                table: "OrderDetailRollbackInfos",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "RollbackProcessedAt",
                table: "OrderDetailRollbackInfos",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "RollbackRequestedAt",
                table: "OrderDetailRollbackInfos",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.AddColumn<string>(
                name: "RollbackTransactionId",
                table: "OrderDetailRollbackInfos",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Payees",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    FullName = table.Column<string>(type: "text", nullable: true),
                    NationalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Created = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: true),
                    LastModified = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<int>(type: "integer", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    DeletedOn = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Payees", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PayeeIbans",
                columns: table => new
                {
                    PayeeId = table.Column<int>(type: "integer", nullable: false),
                    Iban = table.Column<string>(type: "character varying(34)", maxLength: 34, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayeeIbans", x => new { x.PayeeId, x.Iban });
                    table.ForeignKey(
                        name: "FK_PayeeIbans_Payees_PayeeId",
                        column: x => x.PayeeId,
                        principalTable: "Payees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayeeMobileNumbers",
                columns: table => new
                {
                    PayeeId = table.Column<int>(type: "integer", nullable: false),
                    MobileNumber = table.Column<string>(type: "character varying(15)", maxLength: 15, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayeeMobileNumbers", x => new { x.PayeeId, x.MobileNumber });
                    table.ForeignKey(
                        name: "FK_PayeeMobileNumbers_Payees_PayeeId",
                        column: x => x.PayeeId,
                        principalTable: "Payees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayeeUserConfigs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    PayeeId = table.Column<int>(type: "integer", nullable: false),
                    UserConfigId = table.Column<int>(type: "integer", nullable: false),
                    IsBeneficiary = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayeeUserConfigs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PayeeUserConfigs_Payees_PayeeId",
                        column: x => x.PayeeId,
                        principalTable: "Payees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PayeeUserConfigs_UserConfigs_UserConfigId",
                        column: x => x.UserConfigId,
                        principalTable: "UserConfigs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FavoriteIbans",
                columns: table => new
                {
                    PayeeUserConfigId = table.Column<int>(type: "integer", nullable: false),
                    Iban = table.Column<string>(type: "character varying(34)", maxLength: 34, nullable: false),
                    Alias = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FavoriteIbans", x => new { x.PayeeUserConfigId, x.Iban });
                    table.ForeignKey(
                        name: "FK_FavoriteIbans_PayeeUserConfigs_PayeeUserConfigId",
                        column: x => x.PayeeUserConfigId,
                        principalTable: "PayeeUserConfigs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserWalletInformations_UserId",
                table: "UserWalletInformations",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserConfigs_UserId",
                table: "UserConfigs",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Orders_OrderNumber",
                table: "Orders",
                column: "OrderNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Orders_Status",
                table: "Orders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_UserId",
                table: "Orders",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_UserId_Status",
                table: "Orders",
                columns: new[] { "UserId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_Iban",
                table: "OrderDetails",
                column: "Iban");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_PayeeId",
                table: "OrderDetails",
                column: "PayeeId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_UserId",
                table: "OrderDetails",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetailRollbackInfos_IsRollbackCompleted",
                table: "OrderDetailRollbackInfos",
                column: "IsRollbackCompleted");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetailRollbackInfos_RollbackRequestedAt",
                table: "OrderDetailRollbackInfos",
                column: "RollbackRequestedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FavoriteIbans_Iban",
                table: "FavoriteIbans",
                column: "Iban");

            migrationBuilder.CreateIndex(
                name: "IX_PayeeIbans_Iban",
                table: "PayeeIbans",
                column: "Iban");

            migrationBuilder.CreateIndex(
                name: "IX_PayeeMobileNumbers_MobileNumber",
                table: "PayeeMobileNumbers",
                column: "MobileNumber");

            migrationBuilder.CreateIndex(
                name: "IX_Payees_NationalCode",
                table: "Payees",
                column: "NationalCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PayeeUserConfigs_PayeeId_UserConfigId",
                table: "PayeeUserConfigs",
                columns: new[] { "PayeeId", "UserConfigId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PayeeUserConfigs_UserConfigId",
                table: "PayeeUserConfigs",
                column: "UserConfigId");

            migrationBuilder.AddForeignKey(
                name: "FK_OrderDetails_Payees_PayeeId",
                table: "OrderDetails",
                column: "PayeeId",
                principalTable: "Payees",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderDetails_Payees_PayeeId",
                table: "OrderDetails");

            migrationBuilder.DropTable(
                name: "FavoriteIbans");

            migrationBuilder.DropTable(
                name: "PayeeIbans");

            migrationBuilder.DropTable(
                name: "PayeeMobileNumbers");

            migrationBuilder.DropTable(
                name: "PayeeUserConfigs");

            migrationBuilder.DropTable(
                name: "Payees");

            migrationBuilder.DropIndex(
                name: "IX_UserWalletInformations_UserId",
                table: "UserWalletInformations");

            migrationBuilder.DropIndex(
                name: "IX_UserConfigs_UserId",
                table: "UserConfigs");

            migrationBuilder.DropIndex(
                name: "IX_Orders_OrderNumber",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_Orders_Status",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_Orders_UserId",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_Orders_UserId_Status",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_OrderDetails_Iban",
                table: "OrderDetails");

            migrationBuilder.DropIndex(
                name: "IX_OrderDetails_PayeeId",
                table: "OrderDetails");

            migrationBuilder.DropIndex(
                name: "IX_OrderDetails_UserId",
                table: "OrderDetails");

            migrationBuilder.DropIndex(
                name: "IX_OrderDetailRollbackInfos_IsRollbackCompleted",
                table: "OrderDetailRollbackInfos");

            migrationBuilder.DropIndex(
                name: "IX_OrderDetailRollbackInfos_RollbackRequestedAt",
                table: "OrderDetailRollbackInfos");

            migrationBuilder.DropColumn(
                name: "OrderNumber",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "SubmittedTime",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "TotalAmount",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "TotalWageAmount",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "PayeeId",
                table: "OrderDetails");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "OrderDetails");

            migrationBuilder.DropColumn(
                name: "RollbackFailureReason",
                table: "OrderDetailRollbackInfos");

            migrationBuilder.DropColumn(
                name: "RollbackProcessedAt",
                table: "OrderDetailRollbackInfos");

            migrationBuilder.DropColumn(
                name: "RollbackRequestedAt",
                table: "OrderDetailRollbackInfos");

            migrationBuilder.DropColumn(
                name: "RollbackTransactionId",
                table: "OrderDetailRollbackInfos");

            migrationBuilder.RenameColumn(
                name: "IsRollbackCompleted",
                table: "OrderDetailRollbackInfos",
                newName: "TransactionStatus");

            migrationBuilder.AddColumn<string[]>(
                name: "FavoriteIbans",
                table: "UserConfigs",
                type: "text[]",
                nullable: true);
        }
    }
}
