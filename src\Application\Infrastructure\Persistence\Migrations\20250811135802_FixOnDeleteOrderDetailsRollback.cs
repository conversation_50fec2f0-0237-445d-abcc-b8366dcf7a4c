﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Zify.Settlement.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class FixOnDeleteOrderDetailsRollback : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderDetailRollbackInfos_OrderDetails_OrderDetailId",
                table: "OrderDetailRollbackInfos");

            migrationBuilder.AddForeignKey(
                name: "FK_OrderDetailRollbackInfos_OrderDetails_OrderDetailId",
                table: "OrderDetailRollbackInfos",
                column: "OrderDetailId",
                principalTable: "OrderDetails",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderDetailRollbackInfos_OrderDetails_OrderDetailId",
                table: "OrderDetailRollbackInfos");

            migrationBuilder.AddForeignKey(
                name: "FK_OrderDetailRollbackInfos_OrderDetails_OrderDetailId",
                table: "OrderDetailRollbackInfos",
                column: "OrderDetailId",
                principalTable: "OrderDetails",
                principalColumn: "Id");
        }
    }
}
