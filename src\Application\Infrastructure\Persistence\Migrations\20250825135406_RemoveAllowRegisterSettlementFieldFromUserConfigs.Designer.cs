﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Zify.Settlement.Application.Infrastructure.Persistence;

#nullable disable

namespace Zify.Settlement.Application.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250825135406_RemoveAllowRegisterSettlementFieldFromUserConfigs")]
    partial class RemoveAllowRegisterSettlementFieldFromUserConfigs
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Zify.Settlement.Application.Domain.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTimeOffset?>("ScheduledTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("SubmittedTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal>("TotalAmount")
                        .HasPrecision(24, 8)
                        .HasColumnType("numeric(24,8)");

                    b.Property<decimal>("TotalWageAmount")
                        .HasPrecision(24, 8)
                        .HasColumnType("numeric(24,8)");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.Property<Guid?>("WalletBlockCorrelationId")
                        .HasColumnType("uuid")
                        .HasColumnName("WalletBlockCorrelationId");

                    b.Property<Guid?>("WalletWithdrawCorrelationId")
                        .HasColumnType("uuid")
                        .HasColumnName("WalletWithdrawCorrelationId");

                    b.HasKey("Id");

                    b.HasIndex("OrderNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Orders_OrderNumber");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Orders_Status");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_Orders_UserId");

                    b.HasIndex("UserId", "Status")
                        .HasDatabaseName("IX_Orders_UserId_Status");

                    b.ToTable("Orders", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasPrecision(24, 8)
                        .HasColumnType("numeric(24,8)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FullName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Iban")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("Iban");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<string>("Mobile")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<string>("NationalId")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<int?>("OrderDetailRollbackInfoId")
                        .HasColumnType("integer");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<int?>("PayeeId")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.Property<decimal>("WageAmount")
                        .HasPrecision(24, 8)
                        .HasColumnType("numeric(24,8)");

                    b.Property<bool>("WageTransactionStatus")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("WageTransferTransactionId")
                        .HasColumnType("uuid")
                        .HasColumnName("WageTransferTransactionId");

                    b.HasKey("Id");

                    b.HasIndex("Iban")
                        .HasDatabaseName("IX_OrderDetails_Iban");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("IX_OrderDetails_OrderId");

                    b.HasIndex("PayeeId")
                        .HasDatabaseName("IX_OrderDetails_PayeeId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_OrderDetails_UserId");

                    b.ToTable("OrderDetails", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetailRollbackInfo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsRollbackCompleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<Guid>("OrderDetailId")
                        .HasColumnType("uuid");

                    b.Property<string>("RollbackFailureReason")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTimeOffset?>("RollbackProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("RollbackRequestedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RollbackTransactionId")
                        .HasColumnType("text");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.ComplexProperty<Dictionary<string, object>>("RollbackDestinationWalletId", "Zify.Settlement.Application.Domain.OrderDetailRollbackInfo.RollbackDestinationWalletId#WalletId", b1 =>
                        {
                            b1.Property<Guid>("Value")
                                .HasColumnType("uuid")
                                .HasColumnName("RollbackDestinationWalletId");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("RollbackTransferTransactionId", "Zify.Settlement.Application.Domain.OrderDetailRollbackInfo.RollbackTransferTransactionId#CorrelationId", b1 =>
                        {
                            b1.Property<Guid>("Value")
                                .HasColumnType("uuid")
                                .HasColumnName("RollbackTransferTransactionId");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("RollbackWageTransactionId", "Zify.Settlement.Application.Domain.OrderDetailRollbackInfo.RollbackWageTransactionId#CorrelationId", b1 =>
                        {
                            b1.Property<Guid>("Value")
                                .HasColumnType("uuid")
                                .HasColumnName("RollbackWageTransactionId");
                        });

                    b.HasKey("Id");

                    b.HasIndex("IsRollbackCompleted")
                        .HasDatabaseName("IX_OrderDetailRollbackInfos_IsRollbackCompleted");

                    b.HasIndex("OrderDetailId")
                        .IsUnique()
                        .HasDatabaseName("IX_OrderDetailRollbackInfos_OrderDetailId");

                    b.HasIndex("RollbackRequestedAt")
                        .HasDatabaseName("IX_OrderDetailRollbackInfos_RollbackRequestedAt");

                    b.ToTable("OrderDetailRollbackInfos", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.Payee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FullName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<string>("NationalCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("NationalCode")
                        .IsUnique()
                        .HasDatabaseName("IX_Payees_NationalCode");

                    b.ToTable("Payees", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.PayeeUserConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsBeneficiary")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<int>("PayeeId")
                        .HasColumnType("integer");

                    b.Property<int>("UserConfigId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("UserConfigId");

                    b.HasIndex("PayeeId", "UserConfigId")
                        .IsUnique()
                        .HasDatabaseName("IX_PayeeUserConfigs_PayeeId_UserConfigId");

                    b.ToTable("PayeeUserConfigs", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.UserConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AcceptorCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("AuthenticatorTotpEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("AuthenticatorTotpSecretKey")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<decimal>("DailyTransferLimit")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IbanChangeHistory")
                        .HasColumnType("text")
                        .HasColumnName("IbanChangeHistory");

                    b.Property<byte>("IbanUpdateMaxChangesPerWindow")
                        .HasColumnType("smallint");

                    b.Property<byte>("IbanUpdateTimeWindowHours")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsBanned")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCritical")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDepositActivate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFree")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<int>("Max")
                        .HasColumnType("integer");

                    b.Property<int>("MaxSettlementAmount")
                        .HasColumnType("integer");

                    b.Property<int>("Min")
                        .HasColumnType("integer");

                    b.Property<int>("PlanType")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.Property<int>("WageType")
                        .HasColumnType("integer");

                    b.Property<decimal>("WageValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.ComplexProperty<Dictionary<string, object>>("Iban", "Zify.Settlement.Application.Domain.UserConfig.Iban#Iban", b1 =>
                        {
                            b1.Property<string>("Value")
                                .HasColumnType("text")
                                .HasColumnName("Iban");
                        });

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserConfigs_UserId");

                    b.ToTable("UserConfigs", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.UserWalletInformation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<Guid?>("SettlementWalletId")
                        .HasColumnType("uuid")
                        .HasColumnName("SettlementWalletId");

                    b.Property<int>("UserConfigId")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<uint>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.ComplexProperty<Dictionary<string, object>>("PaymentWalletId", "Zify.Settlement.Application.Domain.UserWalletInformation.PaymentWalletId#WalletId", b1 =>
                        {
                            b1.Property<Guid>("Value")
                                .HasColumnType("uuid")
                                .HasColumnName("PaymentWalletId");
                        });

                    b.HasKey("Id");

                    b.HasIndex("UserConfigId")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("UserWalletInformations", (string)null);
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetail", b =>
                {
                    b.HasOne("Zify.Settlement.Application.Domain.Order", "Order")
                        .WithMany("OrderDetails")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zify.Settlement.Application.Domain.Payee", "Payee")
                        .WithMany()
                        .HasForeignKey("PayeeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Order");

                    b.Navigation("Payee");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetailRollbackInfo", b =>
                {
                    b.HasOne("Zify.Settlement.Application.Domain.OrderDetail", "OrderDetail")
                        .WithOne("OrderDetailRollbackInfo")
                        .HasForeignKey("Zify.Settlement.Application.Domain.OrderDetailRollbackInfo", "OrderDetailId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("OrderDetail");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.Payee", b =>
                {
                    b.OwnsMany("Zify.Settlement.Application.Domain.OwnedEntities.PayeeIban", "Ibans", b1 =>
                        {
                            b1.Property<int>("PayeeId")
                                .HasColumnType("integer");

                            b1.Property<string>("Iban")
                                .HasMaxLength(34)
                                .HasColumnType("character varying(34)");

                            b1.HasKey("PayeeId", "Iban");

                            b1.HasIndex("Iban")
                                .HasDatabaseName("IX_PayeeIbans_Iban");

                            b1.ToTable("PayeeIbans", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("PayeeId");
                        });

                    b.OwnsMany("Zify.Settlement.Application.Domain.OwnedEntities.PayeeMobileNumber", "MobileNumbers", b1 =>
                        {
                            b1.Property<int>("PayeeId")
                                .HasColumnType("integer");

                            b1.Property<string>("MobileNumber")
                                .HasMaxLength(15)
                                .HasColumnType("character varying(15)");

                            b1.HasKey("PayeeId", "MobileNumber");

                            b1.HasIndex("MobileNumber")
                                .HasDatabaseName("IX_PayeeMobileNumbers_MobileNumber");

                            b1.ToTable("PayeeMobileNumbers", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("PayeeId");
                        });

                    b.Navigation("Ibans");

                    b.Navigation("MobileNumbers");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.PayeeUserConfig", b =>
                {
                    b.HasOne("Zify.Settlement.Application.Domain.Payee", "Payee")
                        .WithMany("PayeeUserConfigs")
                        .HasForeignKey("PayeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zify.Settlement.Application.Domain.UserConfig", "UserConfig")
                        .WithMany("PayeeUserConfigs")
                        .HasForeignKey("UserConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("Zify.Settlement.Application.Domain.OwnedEntities.FavoriteIban", "FavoriteIbans", b1 =>
                        {
                            b1.Property<int>("PayeeUserConfigId")
                                .HasColumnType("integer");

                            b1.Property<string>("Iban")
                                .HasMaxLength(34)
                                .HasColumnType("character varying(34)");

                            b1.Property<string>("Alias")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("PayeeUserConfigId", "Iban");

                            b1.HasIndex("Iban")
                                .HasDatabaseName("IX_FavoriteIbans_Iban");

                            b1.ToTable("FavoriteIbans", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("PayeeUserConfigId");
                        });

                    b.Navigation("FavoriteIbans");

                    b.Navigation("Payee");

                    b.Navigation("UserConfig");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.UserWalletInformation", b =>
                {
                    b.HasOne("Zify.Settlement.Application.Domain.UserConfig", null)
                        .WithOne("WalletInformation")
                        .HasForeignKey("Zify.Settlement.Application.Domain.UserWalletInformation", "UserConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.Order", b =>
                {
                    b.Navigation("OrderDetails");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.OrderDetail", b =>
                {
                    b.Navigation("OrderDetailRollbackInfo");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.Payee", b =>
                {
                    b.Navigation("PayeeUserConfigs");
                });

            modelBuilder.Entity("Zify.Settlement.Application.Domain.UserConfig", b =>
                {
                    b.Navigation("PayeeUserConfigs");

                    b.Navigation("WalletInformation")
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
