﻿using Zify.Settlement.Application.Common.Interfaces;

namespace Zify.Settlement.Application.Infrastructure.Services.Bank;

public class BankService : IBankService
{
    public Task<object> SingleTransferOrder(string clientTransferRefId, object info)
    {
        throw new NotImplementedException();
    }

    public Task<object> BatchTransferOrder(string clientBatchRefId, object[] items)
    {
        throw new NotImplementedException();
    }
}
