﻿using DNTPersianUtils.Core;
using ErrorOr;
using Microsoft.EntityFrameworkCore;
using PayPing.Integrations.AdminSDK.Interfaces;
using PayPing.Integrations.Models.RabbitExchanges;
using Zify.Settlement.Application.Common.Helpers;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Infrastructure.Services;

public class EventServiceWrapper(
    IEventService eventService,
    IApplicationDbContext dbContext,
    ITotpProvider totpProvider,
    IRedisCacheService cacheService) : IEventServiceWrapper
{
    public async Task SendSettlementWithdrawSuccessfulMessageAsync(
        int userId,
        DateTimeOffset date,
        decimal amount,
        decimal balance)
    {
        await eventService.SettlementWithdrawSuccessfulMessage(new SettlementWithdrawSuccessfulMessage()
        {
            UserId = userId,
            Amount = amount.ToCommas(),
            Date = date.ToPersianDateTextify(),
            CurrentBalance = balance.ToCommas(),
        });
    }

    public async Task SendSettlementTwoFactorVerificationCodeAsync(
        int userId,
        Guid orderId,
        decimal amount,
        CancellationToken cancellationToken = default)
    {
        var twoStepCode = GenerateUniqueNumber();

        await cacheService.SetAsync(
            orderId.ToString(),
            twoStepCode.ToString(),
            absoluteExpiration: TimeSpan.FromMinutes(2),
            cancellationToken: cancellationToken);

        await eventService.SendSettlementTwoFactorVerificationCode(new SendSettlementTwoFactorVerificationCodeMessage()
        {
            UserId = userId,
            Code = twoStepCode.ToString(),
            Amount = (double)amount
        });
    }

    public async Task<ErrorOr<Success>> ValidateTwoStepVerificationAsync(int userId,
        Guid orderId,
        string twoStepCode,
        TwoStepType twoStepType,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(twoStepCode))
            return Error.Validation(description: "کد تایید دومرحله‌ای اجباری می‌باشد");

        if (twoStepType == TwoStepType.SMS)
        {
            var last2Fa = await cacheService.GetAsync<string>(orderId.ToString(), cancellationToken);

            if (string.IsNullOrEmpty(last2Fa))
                return Error.Forbidden(description: "کد تایید شما منقضی شده است. مجدد تلاش کنید");

            if (twoStepCode != last2Fa)
                return Error.Unauthorized(description: "مقدار کد تایید دومرحله‌ای اشتباه می‌باشد");

            return Result.Success;
        }

        var authenticatorTotpSecretKey = await dbContext.UserConfigs
            .Where(x => x.UserId == userId)
            .Select(x => x.AuthenticatorTotpSecretKey)
            .FirstOrDefaultAsync(cancellationToken);

        if (string.IsNullOrWhiteSpace(authenticatorTotpSecretKey))
            return Error.NotFound();

        if (totpProvider.VerifyTotpCode(authenticatorTotpSecretKey, twoStepCode) == false)
            return Error.Forbidden(description: "مقدار کد تایید دومرحله‌ای اشتباه می‌باشد");

        return Result.Success;
    }

    public async Task SendSettlementStatusChangedMessageAsync(
        int userId,
        decimal amount,
        string requestCode,
        string settlementCode,
        string formerStatus,
        string currentStatus,
        string linkToPage)
    {
        await eventService.SendSettlementStatusChangedMessageAsync(new SettlementStatusChangedMessage
        {
            UserId = userId,
            Amount = amount.ToCommas(),
            RequestCode = requestCode,
            SettlementCode = settlementCode,
            CurrentStatus = currentStatus,
            FormerStatus = formerStatus,
            ShortLinkToPage = linkToPage
        });
    }

    public async Task SendIdentifiedDepositMessageAsync(
        int userId,
        string depositId,
        string bankReference,
        decimal amount,
        decimal balanceAfterDeposit)
    {
        await eventService.SendIdentifiedDepositMessageAsync(new IdentifiedDepositMessage
        {
            UserId = userId,
            DepositId = depositId,
            BankReference = bankReference,
            Amount = amount.ToCommas(),
            BalanceAfterDeposit = balanceAfterDeposit.ToCommas()
        });
    }


    private static int GenerateUniqueNumber() => new Random().Next(100000, 999999);
}