﻿using Grpc.Core;
using Grpc.Core.Interceptors;
using Microsoft.Extensions.Logging;

namespace Zify.Settlement.Application.Infrastructure.Services.Grpc.Interceptors;

/// <summary>
/// gRPC interceptor for global exception handling and logging.
/// </summary>
public class ExceptionHandlerInterceptor(
    ILogger<ExceptionHandlerInterceptor> logger) : Interceptor
{
    public override async Task<TResponse> UnaryServerHandler<TRequest, TResponse>(
        TRequest request,
        ServerCallContext context,
        UnaryServerMethod<TRequest, TResponse> continuation)
    {
        var httpContext = context.GetHttpContext();
        var traceId = context.RequestHeaders.GetValue("traceid") ?? httpContext.TraceIdentifier;

        using var scope = logger.BeginScope("TraceId: {TraceId}, Method: {Method}", traceId, context.Method);

        try
        {
            return await continuation(request, context);
        }
        catch (RpcException)
        {
            // Re-throw RpcException directly, as it's an intended gRPC error.
            throw;
        }
        catch (OperationCanceledException)
        {
            logger.LogWarning("Request was cancelled by the client.");
            throw new RpcException(new Status(StatusCode.Cancelled, "Request was cancelled by the client."));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An unhandled error occurred while processing gRPC request.");

            var status = new Status(StatusCode.Internal, "An unexpected internal error occurred.");
            throw new RpcException(status);
        }
    }
}
