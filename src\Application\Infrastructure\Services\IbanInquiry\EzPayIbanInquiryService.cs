﻿using ErrorOr;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Services.EzPay.Models;

namespace Zify.Settlement.Application.Infrastructure.Services.IbanInquiry;

public class EzPayIbanInquiryService(
    IEzPayService ezPayService)
    : IIbanInquiryService
{
    public async Task<ErrorOr<IbanInquiryResponse?>> InquiryIban(string iban)
    {
        var ezPayResult = await ezPayService.InquiryIban(iban);
        if (ezPayResult.IsError)
            return ezPayResult.Errors;

        return MapToGenericResponse(ezPayResult.Value);
    }

    public async Task<ErrorOr<List<IbanInquiryResponse>>> InquiryIbans(string[] ibanList)
    {
        var ezPayResult = await ezPayService.InquiryIbans(ibanList);
        if (ezPayResult.IsError)
            return ezPayResult.Errors;

        return ezPayResult.Value.ConvertAll(MapToGenericResponse);
    }

    public async Task<ErrorOr<string?>> TryValidateIbanAndAccountStatus(string iban)
    {
        var inquiryIban = await InquiryIban(iban);
        var fullName = inquiryIban.Value?.AccountOwners.FirstOrDefault()?.FullName;

        if (!inquiryIban.IsError && (!inquiryIban.Value?.IsActive ?? true))
            return Error.Forbidden(description:
                $"حساب بانکی مربوط به شبای {fullName ?? iban} مسدود می‌باشد");

        return fullName;
    }
    public async Task<ErrorOr<Dictionary<string, string?>>> TryValidateIbanAndAccountStatus(string[] ibans)
    {
        var inquiryIbans = await InquiryIbans(ibans);
        if (inquiryIbans.IsError) return inquiryIbans.Errors;

        List<Error> errors = [];
        Dictionary<string, string?> result = [];
        foreach (var iban in ibans)
        {
            var inquiryIban = inquiryIbans.Value?
                .FirstOrDefault(x => x.Iban == iban);

            var fullName = inquiryIban?
                .AccountOwners.FirstOrDefault()?.FullName;

            if (!inquiryIban?.IsActive ?? true)
            {
                errors.Add(Error.Forbidden(description:
                    $"حساب بانکی مربوط به شبای {fullName ?? iban} مسدود می‌باشد"));
                continue;
            }
            result.Add(iban, fullName);
        }

        return errors.Count == 0 ? result : errors;
    }

    private static IbanInquiryResponse MapToGenericResponse(
        EzPayInquiryIbanResponse? ezPayResponse)
    {
        if (ezPayResponse == null)
            return null!;

        return new IbanInquiryResponse(
            ezPayResponse.Iban,
            ezPayResponse.BankName,
            ezPayResponse.IsActive,
            ezPayResponse.AccountOwners.ConvertAll(owner =>
                new IbanAccountOwnerResponse(owner.FirstName, owner.LastName)));
    }
}
