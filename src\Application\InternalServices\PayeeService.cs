﻿using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.InternalServices;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.InternalServices;
public class PayeeService(
    IApplicationDbContext dbContext,
    IInquiryApiClient inquiryApiClient) : IPayeeService
{
    public async Task<ErrorOr<Payee>> GetOrCreatePayeeAsync(
        PayeeInfo info,
        int userConfigId,
        CancellationToken cancellationToken = default)
    {
        var payee = await dbContext.Payees
            .AsTracking()
            .Include(p => p.Ibans)
            .Include(p => p.PayeeUserConfigs)
            .Where(x => x.NationalCode == info.NationalCode)
            .FirstOrDefaultAsync(cancellationToken);

        if (payee == null)
        {
            payee = Payee.Create(
                nationalCode: info.NationalCode,
                fullName: info.FullName);
            dbContext.Payees.Add(payee);
        }

        var addIban = await AddIban(payee, info.Iban, cancellationToken);
        if (addIban.IsError)
            return addIban.Errors;

        var addMobile = await AddMobileNumber(payee, info.MobileNumber, cancellationToken);
        if (addMobile.IsError)
            return addMobile.Errors;

        var addRelation = AddUserConfigRelation(payee, userConfigId);
        if (addRelation.IsError)
            return addRelation.Errors;

        return payee;
    }

    private ErrorOr<Created> AddUserConfigRelation(Payee payee, int userConfigId)
    {
        if (payee.PayeeUserConfigs.Any(puc => puc.UserConfigId == userConfigId))
            return Result.Created;

        var payeeUserConfig = PayeeUserConfig.Create(payee, userConfigId);
        dbContext.PayeeUserConfigs.Add(payeeUserConfig);

        return Result.Created;
    }

    private async Task<ErrorOr<Created>> AddMobileNumber(Payee payee, string mobileNumber,
        CancellationToken cancellationToken = default)
    {
        if (payee.MobileNumbers.Any(x => x.MobileNumber == mobileNumber))
            return Result.Created;

        var isMatching = await inquiryApiClient.IsMatchingMobileWithNationalCode(
            mobileNumber: mobileNumber,
            nationalCode: payee.NationalCode,
            cancellationToken: cancellationToken);

        if (isMatching.IsError) return isMatching.Errors;

        payee.AddMobileNumber(mobileNumber);
        return Result.Created;
    }

    private async Task<ErrorOr<Created>> AddIban(Payee payee, Iban iban,
        CancellationToken cancellationToken = default)
    {
        if (payee.Ibans.Any(x => x.Iban == iban))
            return Result.Created;

        //TODO : Uncomment and implement the following check when birthdate is available

        //var isMatching = await inquiryApiClient.IsMatchingShebaWithNationalCode(
        //    sheba: iban.Value,
        //    nationalCode: payee.NationalCode,
        //    birthDate: "", // TODO: BirthDate is needed here.
        //    cancellationToken: cancellationToken);

        //if (isMatching.IsError) return isMatching.Errors;

        payee.AddIban(iban);
        return Result.Created;
    }
}
