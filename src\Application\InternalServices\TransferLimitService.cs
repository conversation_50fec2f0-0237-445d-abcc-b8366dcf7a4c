﻿using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.InternalServices;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.InternalServices;

public class TransferLimitService(
    IApplicationDbContext dbContext,
    IDateTime dateTime)
    : ITransferLimitService
{
    private static readonly OrderDetailStatus[] RelevantStatuses = [OrderDetailStatus.InProgress, OrderDetailStatus.Success];

    public async Task<ErrorOr<Success>> CheckDailyTransferLimitAsync(
        int? userId,
        decimal amount,
        decimal dailyLimit,
        CancellationToken cancellationToken = default)
    {
        var todayStart = dateTime.Now.Date;
        var tomorrowStart = todayStart.AddDays(1);

        var totalAmount = await dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.UserId == userId)
            .Where(x => x.Created >= todayStart && x.Created < tomorrowStart)
            .Where(x => RelevantStatuses.Contains(x.Status))
            .SumAsync(x => x.Amount, cancellationToken);

        if (totalAmount + amount > dailyLimit)
        {
            return Error.Conflict(description:
                string.Format(
                    ErrorMessages.MessageFormats.DailyTransferLimitExceeded,
                    amount.ToString("N0"),
                    dailyLimit.ToString("N0"),
                    totalAmount.ToString("N0")
                ));
        }
        return Result.Success;
    }

    public async Task<ErrorOr<Success>> CheckIbanTransferLimitAsync(
        int? userId,
        string iban,
        decimal amount,
        decimal limit,
        CancellationToken cancellationToken = default)
    {
        var todayStart = dateTime.Now.Date;
        var tomorrowStart = todayStart.AddDays(1);

        var totalForIban = await dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.UserId == userId)
            .Where(x => x.Iban == iban)
            .Where(x => x.Created >= todayStart && x.Created < tomorrowStart)
            .Where(x => RelevantStatuses.Contains(x.Status))
            .SumAsync(x => x.Amount, cancellationToken);

        if (totalForIban + amount > limit)
        {
            return Error.Conflict(description: $"امکان واریز بیش از {limit:N0} تومان در ۲۴ساعت برای این شبا {iban} نمی‌باشد");
        }

        return Result.Success;
    }
}
