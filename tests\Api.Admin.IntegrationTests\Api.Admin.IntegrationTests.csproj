﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<AssemblyName>Zify.Settlement.$(MSBuildProjectName)</AssemblyName>
		<RootNamespace>$(AssemblyName)</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.8" />
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="Shouldly" Version="4.3.0" />
		<PackageReference Include="System.Text.Encodings.Web" Version="9.0.8" />
		<PackageReference Include="Testcontainers.PostgreSql" Version="4.6.0" />
		<PackageReference Include="Grpc.Net.Client" Version="2.71.0" />
		<PackageReference Include="Grpc.AspNetCore.Server.ClientFactory" Version="2.71.0" />
		<PackageReference Include="Grpc.Tools" Version="2.71.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.8" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\Application\Application.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

	<ItemGroup>
		<Protobuf Include="..\..\src\Application\Grpc\GrpcServer\Protos\Settlement.proto" GrpcServices="Client" ProtoRoot="..\..\src\Application" />
		<Protobuf Include="..\..\src\Application\Grpc\GrpcServer\Protos\BaseExceptionResponse.proto" ProtoRoot="..\..\src\Application" />
	</ItemGroup>

</Project>
