using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Zify.Settlement.Api.Admin.IntegrationTests.Common;

/// <summary>
/// Test authentication handler that bypasses real JWT validation.
/// It authenticates requests based on the presence of custom HTTP headers.
/// </summary>
public class TestAuthenticationHandler(
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory logger,
    UrlEncoder encoder)
    : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    public const string TestScheme = "Test";
    public const string UserIdHeader = "X-Test-User-Id";
    public const string ScopesHeader = "X-Test-Scopes";
    public const int DefaultTestUserId = 12345;

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        // Check for the User ID header to determine if the user is authenticated.
        if (!Context.Request.Headers.TryGetValue(UserIdHeader, out var userIdValue))
        {
            // If the header is not present, the request is considered unauthenticated.
            return Task.FromResult(AuthenticateResult.Fail("Missing user ID header for test authentication."));
        }

        var userId = userIdValue.FirstOrDefault() ?? DefaultTestUserId.ToString();
        var claims = new List<Claim> { new("sub", userId) };

        // Check for the Scopes header to add specific permissions.
        if (Context.Request.Headers.TryGetValue(ScopesHeader, out var scopesValue))
        {
            var scopes = scopesValue.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);

            claims.AddRange(scopes.Select(scope => new Claim("scope", scope.Trim())));
        }

        var identity = new ClaimsIdentity(claims, TestScheme);
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, TestScheme);

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}