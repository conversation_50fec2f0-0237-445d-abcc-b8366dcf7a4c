using Microsoft.EntityFrameworkCore;
using Settlement;
using Shouldly;
using Zify.Settlement.Api.Admin.IntegrationTests.Common;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Api.Admin.IntegrationTests.Grpc;

public class SettlementGrpcIntegrationTests : GrpcTestBase
{
    [Fact]
    public async Task GetSettlementWalletId_WithExistingUserAndSettlementWallet_ShouldReturnWalletId()
    {
        // Arrange
        const int userId = 123;
        var expectedWalletId = Guid.NewGuid();

        // Setup test data - create user with settlement wallet
        var walletInfo = UserWalletInformation.Create(userId, WalletId.New());
        var userConfig = UserConfig.Create(walletInfo, 1_000_000_000);
        walletInfo.SetSettlementWalletId(expectedWalletId);

        DbContext.UserConfigs.Add(userConfig);
        await DbContext.SaveChangesAsync();

        var client = CreateGrpcClient();
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        // Act
        var response = await client.GetSettlementWalletIdAsync(request);

        // Assert
        response.ShouldNotBeNull();
        response.SettlementWalletId.ShouldBe(expectedWalletId.ToString());
        response.ProblemDetail.ShouldBeNull();
    }

    [Fact]
    public async Task GetSettlementWalletId_WithExistingUserButNoSettlementWallet_ShouldReturnError()
    {
        // Arrange
        const int userId = 456;

        // Setup test data - create user without settlement wallet
        var walletInfo = UserWalletInformation.Create(userId, WalletId.New());
        var userConfig = UserConfig.Create(walletInfo, 1_000_000_000);

        DbContext.UserConfigs.Add(userConfig);
        await DbContext.SaveChangesAsync();

        var client = CreateGrpcClient();
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        // Act
        var response = await client.GetSettlementWalletIdAsync(request);

        // Assert
        response.ShouldNotBeNull();
        response.SettlementWalletId.ShouldBeNullOrEmpty();
        response.ProblemDetail.ShouldNotBeNull();
        response.ProblemDetail.Errors.ShouldNotBeEmpty();
        response.ProblemDetail.Errors[0].Message.ShouldBe("Settlement wallet id not found");
    }

    [Fact]
    public async Task GetSettlementWalletId_WithNonExistentUser_ShouldReturnError()
    {
        // Arrange
        const int nonExistentUserId = 999;
        var client = CreateGrpcClient();
        var request = new GetSettlementWalletIdRequest { UserId = nonExistentUserId };

        // Act
        var response = await client.GetSettlementWalletIdAsync(request);

        // Assert
        response.ShouldNotBeNull();
        response.SettlementWalletId.ShouldBeNullOrEmpty();
        response.ProblemDetail.ShouldNotBeNull();
        response.ProblemDetail.Errors.ShouldNotBeEmpty();
        response.ProblemDetail.Errors[0].Message.ShouldBe("Settlement wallet id not found");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(-100)]
    public async Task GetSettlementWalletId_WithInvalidUserId_ShouldReturnValidationError(int invalidUserId)
    {
        // Arrange
        var client = CreateGrpcClient();
        var request = new GetSettlementWalletIdRequest { UserId = invalidUserId };

        // Act
        var response = await client.GetSettlementWalletIdAsync(request);

        // Assert
        response.ShouldNotBeNull();
        response.SettlementWalletId.ShouldBeNullOrEmpty();
        response.ProblemDetail.ShouldNotBeNull();
        response.ProblemDetail.Errors.ShouldNotBeEmpty();
        response.ProblemDetail.Errors[0].Code.ShouldBe("InvalidUserId");
        response.ProblemDetail.Errors[0].Message.ShouldBe("User ID must be greater than 0.");
    }

    [Fact]
    public async Task GetSettlementWalletId_WithMultipleUsersInDatabase_ShouldReturnCorrectWalletId()
    {
        // Arrange
        const int targetUserId = 111;
        const int otherUserId = 222;
        var targetWalletId = Guid.NewGuid();
        var otherWalletId = Guid.NewGuid();

        // Setup test data - create multiple users
        var targetWalletInfo = UserWalletInformation.Create(targetUserId, WalletId.New());
        var targetUserConfig = UserConfig.Create(targetWalletInfo, 1_000_000_000);
        targetWalletInfo.SetSettlementWalletId(targetWalletId); ;

        var otherWalletInfo = UserWalletInformation.Create(otherUserId, WalletId.New());
        var otherUserConfig = UserConfig.Create(otherWalletInfo, 1_000_000_000);
        otherWalletInfo.SetSettlementWalletId(otherWalletId);

        DbContext.UserConfigs.AddRange(targetUserConfig, otherUserConfig);
        await DbContext.SaveChangesAsync();

        var client = CreateGrpcClient();
        var request = new GetSettlementWalletIdRequest { UserId = targetUserId };

        // Act
        var response = await client.GetSettlementWalletIdAsync(request);

        // Assert
        response.ShouldNotBeNull();
        response.SettlementWalletId.ShouldBe(targetWalletId.ToString());
        response.SettlementWalletId.ShouldNotBe(otherWalletId.ToString());
        response.ProblemDetail.ShouldBeNull();
    }

    [Fact]
    public async Task GetSettlementWalletId_WithConcurrentRequests_ShouldHandleGracefully()
    {
        // Arrange
        const int userId = 333;
        var expectedWalletId = Guid.NewGuid();

        // Setup test data
        var walletInfo = UserWalletInformation.Create(userId, WalletId.New());
        var userConfig = UserConfig.Create(walletInfo, 1_000_000_000);
        walletInfo.SetSettlementWalletId(expectedWalletId);

        DbContext.UserConfigs.Add(userConfig);
        await DbContext.SaveChangesAsync();

        // Act - Send multiple concurrent requests
        var tasks = Enumerable.Range(0, 5)
            .Select(async _ =>
            {
                var client = CreateGrpcClient();
                var request = new GetSettlementWalletIdRequest { UserId = userId };
                return await client.GetSettlementWalletIdAsync(request);
            })
            .ToArray();

        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.ShouldNotBeNull();
        responses.Length.ShouldBe(5);

        foreach (var response in responses)
        {
            response.ShouldNotBeNull();
            response.SettlementWalletId.ShouldBe(expectedWalletId.ToString());
            response.ProblemDetail.ShouldBeNull();
        }
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldPersistDataCorrectly_WhenCalledMultipleTimes()
    {
        // Arrange
        const int userId = 444;
        var expectedWalletId = Guid.NewGuid();

        // Setup test data
        var walletInfo = UserWalletInformation.Create(userId, WalletId.New());
        var userConfig = UserConfig.Create(walletInfo, 1_000_000_000);
        walletInfo.SetSettlementWalletId(expectedWalletId);

        DbContext.UserConfigs.Add(userConfig);
        await DbContext.SaveChangesAsync();

        var client = CreateGrpcClient();
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        // Act - Call multiple times
        var firstResponse = await client.GetSettlementWalletIdAsync(request);
        var secondResponse = await client.GetSettlementWalletIdAsync(request);
        var thirdResponse = await client.GetSettlementWalletIdAsync(request);

        // Assert - All responses should be identical
        firstResponse.SettlementWalletId.ShouldBe(expectedWalletId.ToString());
        secondResponse.SettlementWalletId.ShouldBe(expectedWalletId.ToString());
        thirdResponse.SettlementWalletId.ShouldBe(expectedWalletId.ToString());

        firstResponse.ProblemDetail.ShouldBeNull();
        secondResponse.ProblemDetail.ShouldBeNull();
        thirdResponse.ProblemDetail.ShouldBeNull();

        // Verify data integrity in database
        var dbWalletInfo = await DbContext.UserWalletInformations
            .AsNoTracking()
            .Where(x => x.UserId == userId)
            .FirstOrDefaultAsync();

        dbWalletInfo.ShouldNotBeNull();
        dbWalletInfo.SettlementWalletId?.Value.ShouldBe(expectedWalletId);
    }

    [Theory]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    public async Task GetSettlementWalletId_WithDifferentValidIbans_ShouldWorkCorrectly(string iban)
    {
        // Arrange
        const int userId = 555;
        var expectedWalletId = Guid.NewGuid();

        // Setup test data with different IBANs
        var walletInfo = UserWalletInformation.Create(userId, WalletId.New());
        var userConfig = UserConfig.Create(walletInfo, 1_000_000_000);
        walletInfo.SetSettlementWalletId(expectedWalletId);

        DbContext.UserConfigs.Add(userConfig);
        await DbContext.SaveChangesAsync();

        var client = CreateGrpcClient();
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        // Act
        var response = await client.GetSettlementWalletIdAsync(request);

        // Assert
        response.ShouldNotBeNull();
        response.SettlementWalletId.ShouldBe(expectedWalletId.ToString());
        response.ProblemDetail.ShouldBeNull();

        // Clean up for next iteration
        DbContext.UserConfigs.RemoveRange(DbContext.UserConfigs.Where(x => x.UserId == userId));
        await DbContext.SaveChangesAsync();
    }
}
