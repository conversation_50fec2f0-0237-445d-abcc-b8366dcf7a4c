using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Testcontainers.PostgreSql;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Configurations;
using Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
using Zify.Settlement.Application.Infrastructure.Persistence;
using Zify.Settlement.Application.Infrastructure.Services;

namespace Zify.Settlement.Api.IntegrationTests.Common;

public class CustomWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly PostgreSqlContainer _dbContainer = new PostgreSqlBuilder()
        .WithDatabase("test_db")
        .WithUsername("test_user")
        .WithPassword("test_password")
        .Build();

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            ConfigureCoreServices(services);
            ConfigureTestDatabase(services);
            ConfigureTestAuthentication(services);
            ConfigureMockServices(services);

            services.Configure<UserOptions>(op =>
            {
                op.WageUserId = 100;
                op.IsIbanInquiryActive = true;
                op.IsBankValidationActive = false;
                op.MinSettlementAmount = 1000;
                op.MaxSettlementAmount = **********;
                op.MaxLast24Amount = 5000;
                op.DailyTransferDefaultLimit = *********;
                op.IsFinnotechServiceActive = true;
                op.MaxSettlementCountPerRequest = 50;
                op.IdenticalRequestLimitationHours = 2;
            });
        });

        builder.ConfigureAppConfiguration((_, config) =>
        {
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Jwt:Key"] = "TestKeyThatIsAtLeast32CharactersLongForTesting!",
                ["Jwt:Issuer"] = "TestIssuer",
                ["Jwt:Audience"] = "TestAudience",
                ["Jwt:ExpiryInMinutes"] = "60"
            });
        });

        builder.UseEnvironment("Testing");
        builder.ConfigureLogging(logging =>
        {
            logging.AddConsole();
            logging.SetMinimumLevel(LogLevel.Warning); // Reduce noise in tests
        });
    }

    private static void ConfigureCoreServices(IServiceCollection services)
    {
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddTransient<IDateTime, DateTimeService>();
        services.AddCustomVersioning();
        // Add gRPC server services for testing
        services.AddGrpc(options =>
        {
            options.EnableDetailedErrors = true; // Enable detailed errors for testing
        });
        services.AddGrpcReflection();

        // Add Swagger services for testing to prevent middleware errors
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen();
    }

    private void ConfigureTestDatabase(IServiceCollection services)
    {
        // Remove existing database registrations
        var descriptors = services.Where(d =>
            d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>) ||
            d.ServiceType == typeof(ApplicationDbContext) ||
            d.ServiceType == typeof(IApplicationDbContext))
            .ToList();

        foreach (var descriptor in descriptors)
        {
            services.Remove(descriptor);
        }

        // Add test database context
        services.AddScoped<MockUpdateAuditableInterceptor>();
        services.AddDbContext<IApplicationDbContext, ApplicationDbContext>((sp, options) =>
        {
            options.UseNpgsql(_dbContainer.GetConnectionString())
                .AddInterceptors(sp.GetRequiredService<MockUpdateAuditableInterceptor>());
        });
    }

    private static void ConfigureTestAuthentication(IServiceCollection services)
    {
        services.AddAuthentication(TestAuthenticationHandler.TestScheme)
            .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>(
                TestAuthenticationHandler.TestScheme, _ => { });

        services.AddAuthorizationBuilder()
            .AddPolicy("read", policy => policy.RequireClaim("scope", "settlement:read"))
            .AddPolicy("write", policy => policy.RequireClaim("scope", "settlement:write"))
            .AddPolicy("serviceAdministration", policy => policy.RequireClaim("scope", "settlement:admin"))
            .AddPolicy("accountingAdministration", policy => policy.RequireClaim("administration", "SettlementAccounting"))
            .AddPolicy("walletAdministration", policy => policy.RequireClaim("administration", "SettlementWallet"));
    }

    private static void ConfigureMockServices(IServiceCollection services)
    {
        services.AddSingleton(MockServices.CreateEventService().Object);
        services.AddSingleton(MockServices.CreateWalletService().Object);
    }

    public async Task InitializeAsync()
    {
        await _dbContainer.StartAsync();
        using var scope = Services.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        await dbContext.Database.MigrateAsync();
    }

    public new async Task DisposeAsync()
    {
        await _dbContainer.StopAsync();
        await base.DisposeAsync();
    }
}