using Grpc.Net.Client;
using Settlement;

namespace Zify.Settlement.Api.IntegrationTests.Common;

/// <summary>
/// Base class for gRPC integration tests providing common setup and utilities
/// </summary>
public abstract class GrpcTestBase : IntegrationTestBase
{
    /// <summary>
    /// Creates a gRPC channel configured to use the test server
    /// </summary>
    /// <returns>A configured gRPC channel</returns>
    protected GrpcChannel CreateGrpcChannel()
    {
        var client = Factory.CreateClient();
        return GrpcChannel.ForAddress(client.BaseAddress!, new GrpcChannelOptions
        {
            HttpClient = client
        });
    }

    /// <summary>
    /// Creates a SettlementGrpc client for testing
    /// </summary>
    /// <returns>A configured gRPC client</returns>
    protected SettlementGrpc.SettlementGrpcClient CreateGrpcClient()
    {
        var channel = CreateGrpcChannel();
        return new SettlementGrpc.SettlementGrpcClient(channel);
    }

    /// <summary>
    /// Creates a gRPC client with authentication headers
    /// </summary>
    /// <param name="userId">The user ID to authenticate as</param>
    /// <returns>A configured gRPC client with authentication</returns>
    protected SettlementGrpc.SettlementGrpcClient CreateAuthenticatedGrpcClient(int userId = TestAuthenticationHandler.DefaultTestUserId)
    {
        var httpClient = CreateAuthenticatedClient(userId);
        var channel = GrpcChannel.ForAddress(httpClient.BaseAddress!, new GrpcChannelOptions
        {
            HttpClient = httpClient
        });
        return new SettlementGrpc.SettlementGrpcClient(channel);
    }
}
