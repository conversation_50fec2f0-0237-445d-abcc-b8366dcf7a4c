using Shouldly;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.UnitTests.Domain;

public class OrderCloneTests
{
    [Fact]
    public void Clone_ShouldCreateExactCopyWithNewId_WhenOrderHasNoDetails()
    {
        // Arrange
        var originalOrder = Order.Create(1, "Test Order", "Test Description", DateTimeOffset.Now);

        // Act
        var clonedOrder = originalOrder.Clone();

        // Assert
        clonedOrder.ShouldNotBeNull();
        clonedOrder.Id.ShouldNotBe(originalOrder.Id);
        clonedOrder.Title.ShouldBe(originalOrder.Title);
        clonedOrder.Description.ShouldBe(originalOrder.Description);
        clonedOrder.ScheduledTime.ShouldBe(originalOrder.ScheduledTime);
        clonedOrder.Status.ShouldBe(OrderStatus.Draft);
        clonedOrder.WalletBlockCorrelationId.ShouldBeNull();
        clonedOrder.WalletWithdrawCorrelationId.ShouldBeNull();
        clonedOrder.OrderDetails.Count.ShouldBe(0);
    }

    [Theory]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    public void Clone_ShouldCreateExactCopyWithAllOrderDetails_WhenOrderHasDetails(string iban)
    {
        // Arrange
        const int userId = 1;
        var originalOrder = Order.Create(userId, "Test Order", "Test Description");
        var orderDetail1 = OrderDetail.Create(userId: userId, iban: Iban.Of(iban), amount: 100m, wageAmount: 10m, nationalId: "1234567890", mobile: "09123456789", fullname: "name1", description: "Detail 1");
        var orderDetail2 = OrderDetail.Create(userId, Iban.Of(iban), 200m, 20m, nationalId: "0987654321", mobile: "09987654321", fullname: "name2", description: "Detail 2");

        originalOrder.AddDetail(orderDetail1);
        originalOrder.AddDetail(orderDetail2);

        // Act
        var clonedOrder = originalOrder.Clone();

        // Assert
        clonedOrder.ShouldNotBeNull();
        clonedOrder.Id.ShouldNotBe(originalOrder.Id);
        clonedOrder.OrderDetails.Count.ShouldBe(2);

        // Verify first detail
        var clonedDetail1 = clonedOrder.OrderDetails.First();
        clonedDetail1.Id.ShouldNotBe(orderDetail1.Id);
        clonedDetail1.Iban.Value.ShouldBe(orderDetail1.Iban.Value);
        clonedDetail1.Amount.ShouldBe(orderDetail1.Amount);
        clonedDetail1.WageAmount.ShouldBe(orderDetail1.WageAmount);
        clonedDetail1.NationalId.ShouldBe(orderDetail1.NationalId);
        clonedDetail1.Mobile.ShouldBe(orderDetail1.Mobile);
        clonedDetail1.FullName.ShouldBe(orderDetail1.FullName);
        clonedDetail1.Description.ShouldBe(orderDetail1.Description);
        clonedDetail1.Status.ShouldBe(OrderDetailStatus.Init);

        // Verify second detail
        var clonedDetail2 = clonedOrder.OrderDetails.Last();
        clonedDetail2.Id.ShouldNotBe(orderDetail2.Id);
        clonedDetail2.Iban.Value.ShouldBe(orderDetail2.Iban.Value);
        clonedDetail2.Amount.ShouldBe(orderDetail2.Amount);
        clonedDetail2.WageAmount.ShouldBe(orderDetail2.WageAmount);
        clonedDetail2.NationalId.ShouldBe(orderDetail2.NationalId);
        clonedDetail2.Mobile.ShouldBe(orderDetail2.Mobile);
        clonedDetail2.FullName.ShouldBe(orderDetail2.FullName);
        clonedDetail2.Description.ShouldBe(orderDetail2.Description);
        clonedDetail2.Status.ShouldBe(OrderDetailStatus.Init);
    }

    [Fact]
    public void Clone_ShouldResetWalletCorrelationIds_WhenOriginalOrderHasWalletIds()
    {
        // Arrange
        var originalOrder = Order.Create(1, "Test Order", "Test Description");
        originalOrder.WalletProcessing(); // This sets WalletBlockCorrelationId

        // Act
        var clonedOrder = originalOrder.Clone();

        // Assert
        clonedOrder.WalletBlockCorrelationId.ShouldBeNull();
        clonedOrder.WalletWithdrawCorrelationId.ShouldBeNull();
        originalOrder.WalletBlockCorrelationId.ShouldNotBeNull(); // Original should still have it
    }

    [Fact]
    public void Clone_ShouldAlwaysSetStatusToDraft_RegardlessOfOriginalStatus()
    {
        // Arrange
        var originalOrder = Order.Create(1, "Test Order", "Test Description");
        originalOrder.WalletProcessing();
        originalOrder.Submit(DateTimeOffset.UtcNow);
        originalOrder.Process();

        // Act
        var clonedOrder = originalOrder.Clone();

        // Assert
        clonedOrder.Status.ShouldBe(OrderStatus.Draft);
        originalOrder.Status.ShouldBe(OrderStatus.Processing); // Original should remain unchanged
    }

    [Fact]
    public void Clone_ShouldCreateUniqueIdsForAllOrderDetails_WhenMultipleDetailsExist()
    {
        // Arrange
        var originalOrder = Order.Create(1, "Test Order", "Test Description");
        var iban = Iban.Of("**************************");

        for (int i = 0; i < 5; i++)
        {
            var detail = OrderDetail.Create(1, iban, 100m + i, 10m + i, payeeId: i, $"123456789{i}", $"0912345678{i}", $"Detail {i}");
            originalOrder.AddDetail(detail);
        }

        // Act
        var clonedOrder = originalOrder.Clone();

        // Assert
        clonedOrder.OrderDetails.Count.ShouldBe(5);

        var originalIds = originalOrder.OrderDetails.Select(d => d.Id).ToList();
        var clonedIds = clonedOrder.OrderDetails.Select(d => d.Id).ToList();

        // All cloned IDs should be different from original IDs
        clonedIds.ShouldAllBe(id => !originalIds.Contains(id));

        // All cloned IDs should be unique
        clonedIds.Distinct().Count().ShouldBe(5);
    }
}
