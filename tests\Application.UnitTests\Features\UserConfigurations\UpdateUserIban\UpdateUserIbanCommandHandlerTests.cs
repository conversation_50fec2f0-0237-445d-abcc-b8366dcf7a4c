using Microsoft.EntityFrameworkCore;
using Moq;
using Shouldly;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Features.UserConfigurations;
using Zify.Settlement.Application.Infrastructure.Persistence;
using Zify.Settlement.Application.UnitTests.Infrastructure;

namespace Zify.Settlement.Application.UnitTests.Features.UserConfigurations.UpdateUserIban;

public class UpdateUserIbanCommandHandlerTests
{
    private readonly Mock<ICurrentUserService> _mockCurrentUserService;
    private readonly Mock<IDateTime> _mockDateTime;
    private readonly Mock<IApplicationDbContext> _mockDbContext;
    private readonly Mock<IInquiryApiClient> _mockInquiryService;
    private readonly Mock<DbSet<UserConfig>> _mockUserConfigsDbSet;
    private readonly UpdateUserIbanCommandHandler _handler;

    public UpdateUserIbanCommandHandlerTests()
    {
        _mockCurrentUserService = new Mock<ICurrentUserService>();
        _mockDateTime = new Mock<IDateTime>();
        _mockDbContext = new Mock<IApplicationDbContext>();
        _mockUserConfigsDbSet = MockDbSetHelper.CreateEmptyMockDbSet<UserConfig>();
        _mockInquiryService = new Mock<IInquiryApiClient>();

        _mockDbContext.Setup(x => x.UserConfigs).Returns(_mockUserConfigsDbSet.Object);
        _mockDbContext.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>())).ReturnsAsync(1);

        _handler = new UpdateUserIbanCommandHandler(_mockDbContext.Object,
            _mockCurrentUserService.Object,
            _mockDateTime.Object,
            _mockInquiryService.Object);
    }

    private UserConfig CreateUserConfig(int userId)
    {
        var walletInfo = UserWalletInformation.Create(userId, Guid.NewGuid());
        var userConfig = UserConfig.Create(walletInfo, 800_000_000);
        return userConfig;
    }

    private void SetupMockDbSetWithUserConfig(UserConfig userConfig)
    {
        var userConfigs = new List<UserConfig> { userConfig };
        var mockDbSet = MockDbSetHelper.CreateMockDbSet(userConfigs);
        _mockDbContext.Setup(x => x.UserConfigs).Returns(mockDbSet.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnSuccess_WhenValidIbanAndUnderRateLimit()
    {
        // Arrange
        var userId = 123;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = CreateUserConfig(userId);
        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify the user config was updated
        userConfig.Iban.Value.ShouldBe(newIban);
        userConfig.IbanChangeHistory.ShouldContain(currentTime);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnNotFound_WhenUserConfigDoesNotExist()
    {
        // Arrange
        var userId = 999;
        var newIban = "**************************";

        // Setup empty user configs (no user config exists)
        // No additional setup needed for empty DbSet

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(DateTime.UtcNow);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorOr.ErrorType.NotFound);
        result.FirstError.Description.ShouldBe("اطلاعات کاربری یافت نشد.");
    }

    [Fact]
    public async Task Handle_ShouldReturnForbidden_WhenRateLimitExceeded()
    {
        // Arrange
        var userId = 456;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = CreateUserConfig(userId);

        // Add a change within the time window to exceed the rate limit (default is 1 per 24 hours)
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-12));

        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorOr.ErrorType.Forbidden);
        result.FirstError.Description.ShouldContain("شما در هر");
        result.FirstError.Description.ShouldContain("ساعت تنها");
        result.FirstError.Description.ShouldContain("بار می‌توانید");
        result.FirstError.Description.ShouldContain("شماره شبا");
    }

    [Fact]
    public async Task Handle_ShouldIncludeConfigurationValuesInErrorMessage_WhenRateLimitExceeded()
    {
        // Arrange
        var userId = 789;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = CreateUserConfig(userId);

        // Add a change to exceed the rate limit
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-12));

        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();

        // The error message should contain the configuration values
        // Default values: 24 hours window, 1 max change
        result.FirstError.Description.ShouldContain("24");
        result.FirstError.Description.ShouldContain("1");
    }

    [Fact]
    public async Task Handle_ShouldNotUpdateDatabase_WhenIbanIsSame()
    {
        // Arrange
        var userId = 321;
        var currentTime = DateTime.UtcNow;
        var iban = Iban.Of("**************************");

        var userConfig = CreateUserConfig(userId);
        userConfig.UpdateIban(iban, currentTime.AddHours(-25));

        var originalIban = userConfig.Iban.Value;
        var originalHistoryCount = userConfig.IbanChangeHistory.Count;

        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(originalIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify the IBAN remains the same and history count doesn't change
        userConfig.Iban.Value.ShouldBe(originalIban);
        userConfig.IbanChangeHistory.Count.ShouldBe(originalHistoryCount); // Should not change

        // Verify SaveChangesAsync was still called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldAllowUpdate_WhenPreviousChangesAreOutsideTimeWindow()
    {
        // Arrange
        var userId = 654;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = CreateUserConfig(userId);

        // Add changes outside the 24-hour window
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-25));
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-30));

        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify the user config was updated
        userConfig.Iban.Value.ShouldBe(newIban);
        userConfig.IbanChangeHistory.ShouldContain(currentTime);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldCleanUpOldHistoryEntries_WhenUpdating()
    {
        // Arrange
        var userId = 987;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = CreateUserConfig(userId);

        // Add old entries that should be cleaned up
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-25));
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-30));
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-48));

        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify the user config was updated and old history entries were cleaned up
        userConfig.IbanChangeHistory.Count.ShouldBe(1); // Only the new entry should remain
        userConfig.IbanChangeHistory.ShouldContain(currentTime);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    public async Task Handle_ShouldAcceptValidIranianIbans_WhenUnderRateLimit(string validIban)
    {
        // Arrange
        var userId = 555;
        var currentTime = DateTime.UtcNow;

        var userConfig = CreateUserConfig(userId);
        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(validIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify the user config was updated
        userConfig.Iban.Value.ShouldBe(validIban);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldPersistChangesToDatabase_WhenSuccessful()
    {
        // Arrange
        var userId = 111;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = CreateUserConfig(userId);
        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify the user config was updated
        userConfig.Iban.Value.ShouldBe(newIban);

        // Verify SaveChangesAsync was called to persist changes
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldNotModifyOtherUsers_WhenUpdatingIban()
    {
        // Arrange
        var userId1 = 222;
        var userId2 = 333;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig1 = CreateUserConfig(userId1);
        var userConfig2 = CreateUserConfig(userId2);
        var originalIban2 = userConfig2.Iban.Value;

        // Setup mock to return only the target user's config (simulating the Where clause)
        SetupMockDbSetWithUserConfig(userConfig1);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId1);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify only the target user's config was updated
        userConfig1.Iban.Value.ShouldBe(newIban);

        // Verify the other user's config remains unchanged (since it wasn't in the mock result)
        userConfig2.Iban.Value.ShouldBe(originalIban2); // Should remain unchanged

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldUseCurrentUserServiceUserId_WhenFindingUserConfig()
    {
        // Arrange
        var actualUserId = 777;
        var differentUserId = 888;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var actualUserConfig = CreateUserConfig(actualUserId);
        var differentUserConfig = CreateUserConfig(differentUserId);

        // Setup mock to return only the actual user's config (simulating the Where clause)
        SetupMockDbSetWithUserConfig(actualUserConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(actualUserId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify only the correct user's IBAN was updated
        actualUserConfig.Iban.Value.ShouldBe(newIban);

        // Verify the other user's config remains unchanged (since it wasn't in the mock result)
        differentUserConfig.Iban.Value.ShouldNotBe(newIban);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldUseDateTimeServiceNow_WhenUpdatingIban()
    {
        // Arrange
        var userId = 999;
        var newIban = "**************************";
        var specificTime = new DateTime(2024, 1, 15, 10, 30, 45, DateTimeKind.Utc);

        var userConfig = CreateUserConfig(userId);
        SetupMockDbSetWithUserConfig(userConfig);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(specificTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify the specific time was used from the DateTime service
        userConfig.IbanChangeHistory.ShouldContain(specificTime);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
}
